# 🔌 详细接线报告 - 12V转5V/3.3V供电方案

## 📋 项目概述

**项目名称**: 8路灰度传感器循迹小车  
**主控芯片**: MSPM0G3507  
**供电方案**: 12V → 5V/3.3V转换模块  
**传感器**: Ganwei 8路MCU-less灰度传感器  
**电机驱动**: TB6612FNG双电机驱动  

## ⚡ 供电系统设计

### 电源拓扑结构
```
12V锂电池/电源适配器
         ↓
    12V→5V/3.3V转换模块
         ↓
    ┌─────────┬─────────┐
    │   5V    │  3.3V   │
    │  供电轨  │  供电轨  │
    └─────────┴─────────┘
         ↓         ↓
    ┌─────────┬─────────┐
    │ 传感器   │ 主控板   │
    │ 电机驱动 │ 逻辑电路 │
    └─────────┴─────────┘
```

### 电流需求分析
```
设备功耗估算：
┌─────────────────────────────────────┐
│ 设备           │ 电压  │ 电流   │ 功耗 │
├─────────────────────────────────────┤
│ MSPM0G3507    │ 3.3V │ 50mA  │ 0.17W│
│ 8路灰度传感器  │ 5V   │ 100mA │ 0.5W │
│ TB6612驱动    │ 5V   │ 50mA  │ 0.25W│
│ 双电机(空载)  │ 12V  │ 200mA │ 2.4W │
│ LED指示灯     │ 3.3V │ 20mA  │ 0.07W│
├─────────────────────────────────────┤
│ 总计          │      │       │ 3.4W │
│ 建议电源容量   │ 12V  │ 500mA │ 6W   │
└─────────────────────────────────────┘
```

## 🔌 详细接线指南

### 1. 电源模块连接

#### 12V转5V/3.3V模块
```
输入端 (12V电源)：
┌─────────────────────────────────────┐
│ 12V电源正极 → VIN+  (红色粗线)       │
│ 12V电源负极 → VIN-  (黑色粗线)       │
│ 电源地线   → GND   (黑色粗线)       │
└─────────────────────────────────────┘

输出端 (分配给各模块)：
┌─────────────────────────────────────┐
│ +5V输出   → 5V供电轨   (红色线)      │
│ +3.3V输出 → 3.3V供电轨 (橙色线)      │
│ GND输出   → 公共地线   (黑色线)      │
└─────────────────────────────────────┘
```

#### 供电轨分配
```
5V供电轨负载：
├── 8路灰度传感器 VCC
├── TB6612驱动模块 VCC  
├── TB6612驱动模块 STBY
└── 预留扩展接口

3.3V供电轨负载：
├── MSPM0G3507 VCC
├── MSPM0G3507 VBAT (可选)
└── 预留扩展接口

公共地线：
├── 所有模块的GND
├── 按键公共端
└── LED负极
```

### 2. MSPM0G3507主控板连接

#### 电源连接
```c
// 对应main.c中的系统初始化
clock_init(SYSTEM_CLOCK_120M);
debug_init();

主控板电源：
┌─────────────────────────────────────┐
│ 主控引脚   │ 连接目标    │ 线色      │
├─────────────────────────────────────┤
│ VCC       │ +3.3V      │ 橙色线    │
│ GND       │ 公共地线    │ 黑色线    │
│ VBAT      │ +3.3V      │ 橙色线    │ ← 可选，RTC供电
└─────────────────────────────────────┘
```

#### 调试接口
```c
// 对应main.c中的调试初始化
debug_init();

调试串口 (UART0)：
┌─────────────────────────────────────┐
│ 功能      │ 引脚       │ 连接       │
├─────────────────────────────────────┤
│ TX       │ A8         │ USB转串口RX │
│ RX       │ A9         │ USB转串口TX │
│ GND      │ GND        │ USB转串口GND│
│ 波特率    │ 115200     │ 8N1        │
└─────────────────────────────────────┘
```

### 3. 8路灰度传感器连接 (关键！)

#### 传感器供电
```c
// 对应main.c中的传感器初始化代码
ganwei_grayscale_init(&grayscale_sensor, GANWEI_GRAYSCALE_CLASS_EDITION,
                     GANWEI_GRAYSCALE_ADC_12BITS, A0, A1, A2, ADC0_CH0_A27);

8路灰度传感器：
┌─────────────────────────────────────┐
│ 传感器引脚 │ 连接目标    │ 线色      │
├─────────────────────────────────────┤
│ VCC       │ +5V        │ 红色线    │ ← 重要：必须5V供电！
│ GND       │ 公共地线    │ 黑色线    │
│ EN        │ A26        │ 绿色线    │ ← 使能信号
│ AD0       │ A0         │ 黄色线    │ ← 地址线0
│ AD1       │ A1         │ 蓝色线    │ ← 地址线1  
│ AD2       │ A2         │ 紫色线    │ ← 地址线2
│ OUT       │ A27        │ 白色线    │ ← ADC数据输出
└─────────────────────────────────────┘
```

#### 使能信号配置
```c
// 对应main.c中的使能信号设置
gpio_init(A26, GPO, GPIO_LOW, GPO_PUSH_PULL);  // EN引脚，低电平使能

使能信号说明：
┌─────────────────────────────────────┐
│ EN引脚状态 │ 传感器状态  │ 说明      │
├─────────────────────────────────────┤
│ 低电平(0V) │ 工作状态    │ 正常采集   │
│ 高电平(3.3V)│ 待机状态   │ 省电模式   │
│ 悬空      │ 工作状态    │ 默认使能   │
└─────────────────────────────────────┘
```

### 4. 电机驱动模块连接

#### TB6612电机驱动
```c
// 对应main.c中的电机初始化代码
gpio_init(B22, GPO, GPIO_HIGH, GPO_PUSH_PULL);  // 左电机方向1
gpio_init(B23, GPO, GPIO_HIGH, GPO_PUSH_PULL);  // 左电机方向2
pwm_init(PWM_TIM_A0_CH1_A7, 17000, 0);          // 左电机PWM

TB6612驱动模块：
┌─────────────────────────────────────┐
│ 驱动引脚   │ 连接目标    │ 线色      │
├─────────────────────────────────────┤
│ VCC       │ +5V        │ 红色线    │ ← 逻辑电源5V
│ VM        │ +12V       │ 红粗线    │ ← 电机电源12V
│ GND       │ 公共地线    │ 黑色线    │
│ AIN1      │ B22        │ 黄色线    │ ← 左电机方向1
│ AIN2      │ B23        │ 蓝色线    │ ← 左电机方向2
│ PWMA      │ A7         │ 绿色线    │ ← 左电机PWM
│ BIN1      │ B24        │ 紫色线    │ ← 右电机方向1
│ BIN2      │ B25        │ 白色线    │ ← 右电机方向2
│ PWMB      │ B26        │ 灰色线    │ ← 右电机PWM
│ STBY      │ +5V        │ 红色线    │ ← 待机控制
└─────────────────────────────────────┘
```

#### 电机连接
```
左电机 (Motor A)：
┌─────────────────────────────────────┐
│ 电机线     │ 连接目标    │ 说明      │
├─────────────────────────────────────┤
│ 红色线     │ AO1        │ 电机正极   │
│ 黑色线     │ AO2        │ 电机负极   │
└─────────────────────────────────────┘

右电机 (Motor B)：
┌─────────────────────────────────────┐
│ 电机线     │ 连接目标    │ 说明      │
├─────────────────────────────────────┤
│ 红色线     │ BO1        │ 电机正极   │
│ 黑色线     │ BO2        │ 电机负极   │
└─────────────────────────────────────┘
```

### 5. 按键和LED连接

#### 按键接口
```c
// 对应main.c中的按键定义
uint8 key_state[4] = {1,1,1,1}; // 按键当前状态

按键连接 (上拉输入)：
┌─────────────────────────────────────┐
│ 按键      │ 连接引脚    │ 功能        │
├─────────────────────────────────────┤
│ KEY1      │ A10        │ 参数选择     │
│ KEY2      │ A11        │ 参数增加     │
│ KEY3      │ A12        │ 参数减少     │
│ KEY4      │ A13        │ 启动/停止    │
│ 公共端    │ GND        │ 按键地线     │
└─────────────────────────────────────┘
```

#### LED指示灯
```c
// 对应main.c中的LED初始化
gpio_init(B0, GPO, GPIO_LOW, GPO_PUSH_PULL);   // LED1
gpio_init(B1, GPO, GPIO_LOW, GPO_PUSH_PULL);   // LED2

LED连接：
┌─────────────────────────────────────┐
│ LED       │ 连接引脚    │ 功能        │
├─────────────────────────────────────┤
│ LED1正极  │ B0         │ 状态指示     │
│ LED1负极  │ 330Ω→GND   │ 限流电阻     │
│ LED2正极  │ B1         │ 圈数指示     │
│ LED2负极  │ 330Ω→GND   │ 限流电阻     │
└─────────────────────────────────────┘
```

## 🔧 接线实施步骤

### 步骤1：电源系统搭建

#### 1.1 转换模块安装
```
安装位置建议：
┌─────────────────────────────────────┐
│ • 远离电机和高频器件，减少干扰        │
│ • 通风良好位置，避免过热             │
│ • 便于接线，预留维护空间             │
│ • 使用螺丝固定，避免松动             │
└─────────────────────────────────────┘
```

#### 1.2 供电轨制作
```
建议使用面包板或万能板制作供电轨：

5V供电轨 (红色导线)：
├── 转换模块5V输出
├── 灰度传感器VCC
├── TB6612 VCC
├── TB6612 STBY
└── 预留接口×2

3.3V供电轨 (橙色导线)：
├── 转换模块3.3V输出
├── MSPM0G3507 VCC
├── MSPM0G3507 VBAT
└── 预留接口×2

公共地线 (黑色导线)：
├── 转换模块GND输出
├── 所有模块GND
├── 按键公共端
├── LED负极
└── 预留接口×3
```

### 步骤2：主控板连接

#### 2.1 电源连接验证
```
连接顺序：
1. 先连接GND (黑线)
2. 再连接VCC (橙线)
3. 最后连接VBAT (橙线，可选)

验证方法：
1. 用万用表测量VCC引脚电压 = 3.3V±0.1V
2. 用万用表测量GND引脚电压 = 0V
3. 观察主控板电源指示灯亮起
```

#### 2.2 调试接口连接
```
USB转串口模块连接：
┌─────────────────────────────────────┐
│ USB转串口  │ MSPM0G3507 │ 说明      │
├─────────────────────────────────────┤
│ RX        │ A8 (TX)    │ 接收数据   │
│ TX        │ A9 (RX)    │ 发送数据   │
│ GND       │ GND        │ 公共地线   │
│ VCC       │ 不连接      │ 避免冲突   │
└─────────────────────────────────────┘

测试方法：
1. 打开串口调试助手，115200波特率
2. 复位主控板
3. 应该看到启动信息输出
```

### 步骤3：传感器连接 (重点！)

#### 3.1 传感器供电连接
```
⚠️ 重要提醒：传感器必须使用5V供电！

连接顺序：
1. 首先连接GND (黑线)
2. 然后连接VCC到5V (红线) ← 关键！
3. 暂时不连接信号线

验证方法：
1. 用万用表测量传感器VCC = 5.0V±0.2V
2. 用万用表测量传感器GND = 0V
3. 观察传感器工作指示灯(如有)
```

#### 3.2 信号线连接
```c
// 严格按照main.c中的引脚定义连接
ganwei_grayscale_init(&grayscale_sensor, GANWEI_GRAYSCALE_CLASS_EDITION,
                     GANWEI_GRAYSCALE_ADC_12BITS, A0, A1, A2, ADC0_CH0_A27);

信号线连接顺序：
1. EN  → A26 (绿线) - 使能信号
2. AD0 → A0  (黄线) - 地址线0
3. AD1 → A1  (蓝线) - 地址线1
4. AD2 → A2  (紫线) - 地址线2
5. OUT → A27 (白线) - ADC输出

⚠️ 注意事项：
• 使能信号EN默认应该输出低电平(0V)
• 地址线用于选择8个传感器通道
• ADC输出线负责传输模拟量数据
```

#### 3.3 传感器功能测试
```
测试步骤：
1. 下载程序到主控板
2. 打开串口调试助手
3. 观察传感器自检信息：
   "=== 传感器自检 ==="
   "8路传感器数据: 3500 3600 3400 3550 3480 3520 3610 3580"
   "✅ 传感器工作正常"

4. 手动遮挡传感器，观察数值变化
5. 白色表面：数值较高 (3000-4000)
6. 黑色表面：数值较低 (100-500)
```

### 步骤4：电机驱动连接

#### 4.1 驱动模块供电
```
TB6612供电连接：
┌─────────────────────────────────────┐
│ 连接顺序   │ 注意事项              │
├─────────────────────────────────────┤
│ 1. GND    │ 先连接地线，确保安全    │
│ 2. VCC    │ 5V逻辑电源            │
│ 3. VM     │ 12V电机电源 (大电流)   │
│ 4. STBY   │ 连接到5V，使能驱动     │
└─────────────────────────────────────┘

⚠️ 重要提醒：
• VM引脚需要大电流，使用粗导线(≥18AWG)
• 在VM和GND之间并联1000μF电容，减少电机启动冲击
• 确保12V电源能提供足够电流(≥1A)
```

#### 4.2 控制信号连接
```c
// 对应main.c中的电机控制引脚
gpio_init(B22, GPO, GPIO_HIGH, GPO_PUSH_PULL);  // AIN1
gpio_init(B23, GPO, GPIO_HIGH, GPO_PUSH_PULL);  // AIN2
pwm_init(PWM_TIM_A0_CH1_A7, 17000, 0);          // PWMA

控制信号连接：
┌─────────────────────────────────────┐
│ 主控引脚   │ TB6612引脚 │ 功能      │
├─────────────────────────────────────┤
│ B22       │ AIN1      │ 左电机方向1 │
│ B23       │ AIN2      │ 左电机方向2 │
│ A7        │ PWMA      │ 左电机速度  │
│ B24       │ BIN1      │ 右电机方向1 │
│ B25       │ BIN2      │ 右电机方向2 │
│ B26       │ PWMB      │ 右电机速度  │
└─────────────────────────────────────┘
```

#### 4.3 电机连接
```
电机接线：
┌─────────────────────────────────────┐
│ 电机类型   │ 接线方法              │
├─────────────────────────────────────┤
│ 有刷直流电机│ 红线→AO1, 黑线→AO2    │
│ 编码器电机  │ 红线→AO1, 黑线→AO2    │
│           │ 编码器单独供电和信号   │
│ 减速电机   │ 按照电机标识连接       │
└─────────────────────────────────────┘

测试方法：
1. 先不连接电机，测试控制信号
2. 用万用表测量AIN1、AIN2电平变化
3. 用示波器观察PWM波形(17kHz)
4. 确认无误后连接电机测试
```

### 步骤5：用户接口连接

#### 5.1 按键连接
```c
// 按键配置为上拉输入，按下时为低电平
gpio_init(A10, GPI, GPIO_HIGH, GPI_PULL_UP);  // KEY1

按键接线：
┌─────────────────────────────────────┐
│ 按键编号   │ 引脚连接              │
├─────────────────────────────────────┤
│ KEY1      │ 一端→A10, 另一端→GND   │
│ KEY2      │ 一端→A11, 另一端→GND   │
│ KEY3      │ 一端→A12, 另一端→GND   │
│ KEY4      │ 一端→A13, 另一端→GND   │
└─────────────────────────────────────┘

按键功能：
• KEY1: 参数选择 (圈数→直道速度→转弯速度→灵敏度)
• KEY2: 参数数值增加
• KEY3: 参数数值减少
• KEY4: 启动/停止循迹
```

#### 5.2 LED指示灯连接
```c
// LED配置为推挽输出，高电平点亮
gpio_init(B0, GPO, GPIO_LOW, GPO_PUSH_PULL);   // LED1

LED接线：
┌─────────────────────────────────────┐
│ LED       │ 接线方法              │
├─────────────────────────────────────┤
│ LED1      │ 正极→B0, 负极→330Ω→GND │
│ LED2      │ 正极→B1, 负极→330Ω→GND │
└─────────────────────────────────────┘

LED功能：
• LED1: 系统状态指示 (待机/运行/错误)
• LED2: 圈数指示 (闪烁次数=当前圈数)
```

## ⚠️ 安全注意事项

### 电源安全
```
⚠️ 重要安全提醒：
┌─────────────────────────────────────┐
│ • 先连接地线，后连接电源线           │
│ • 检查极性，避免反接损坏器件         │
│ • 使用保险丝保护12V电源             │
│ • 电机电源与逻辑电源分离             │
│ • 在电源输入端并联滤波电容           │
└─────────────────────────────────────┘
```

### 接线安全
```
⚠️ 接线规范：
┌─────────────────────────────────────┐
│ • 使用合适线径，大电流用粗线         │
│ • 接线牢固，避免虚接造成发热         │
│ • 信号线远离电源线，减少干扰         │
│ • 预留维护空间，便于故障排查         │
│ • 标记线缆功能，便于后期维护         │
└─────────────────────────────────────┘
```

### 测试安全
```
⚠️ 测试注意事项：
┌─────────────────────────────────────┐
│ • 分步测试，先电源后功能             │
│ • 使用万用表验证电压电流             │
│ • 观察器件温度，避免过热             │
│ • 准备急停开关，紧急情况断电         │
│ • 在安全环境测试，避免意外伤害       │
└─────────────────────────────────────┘
```

## 🧪 系统测试验证

### 测试阶段1：电源系统验证

#### 电压测试
```
使用万用表测量各关键点电压：
┌─────────────────────────────────────┐
│ 测试点     │ 期望电压    │ 容差      │
├─────────────────────────────────────┤
│ 12V输入   │ 12.0V      │ ±0.5V     │
│ 5V输出    │ 5.0V       │ ±0.2V     │
│ 3.3V输出  │ 3.3V       │ ±0.1V     │
│ 主控VCC   │ 3.3V       │ ±0.1V     │
│ 传感器VCC │ 5.0V       │ ±0.2V     │
│ 驱动VCC   │ 5.0V       │ ±0.2V     │
│ 驱动VM    │ 12.0V      │ ±0.5V     │
└─────────────────────────────────────┘
```

#### 电流测试
```
测量各模块工作电流：
┌─────────────────────────────────────┐
│ 模块       │ 期望电流    │ 最大电流   │
├─────────────────────────────────────┤
│ 主控板     │ 30-50mA    │ 100mA     │
│ 灰度传感器  │ 80-120mA   │ 150mA     │
│ 电机驱动   │ 30-50mA    │ 100mA     │
│ 电机(空载) │ 100-200mA  │ 500mA     │
│ LED指示    │ 10-20mA    │ 50mA      │
│ 系统总计   │ 250-440mA  │ 900mA     │
└─────────────────────────────────────┘
```

### 测试阶段2：功能模块验证

#### 串口通信测试
```
预期串口输出：
=== 竞赛巡线系统启动 ===
硬件初始化完成
=== 传感器自检 ===
8路传感器数据: 3500 3600 3400 3550 3480 3520 3610 3580
✅ 传感器工作正常
上电直接运行模式 - 目标3圈
参数: 直道500 转弯250 灵敏度6
开始巡线...
```

#### 传感器数据测试
```
传感器响应测试：
┌─────────────────────────────────────┐
│ 测试条件   │ 期望数值    │ 说明      │
├─────────────────────────────────────┤
│ 白色纸张   │ 3000-4000  │ 高反射率   │
│ 黑色胶带   │ 100-500    │ 低反射率   │
│ 手指遮挡   │ 500-1500   │ 中等反射   │
│ 环境光变化 │ 数值稳定    │ 自适应能力 │
└─────────────────────────────────────┘
```

#### 电机控制测试
```
电机响应测试：
┌─────────────────────────────────────┐
│ 测试项目   │ 验证方法    │ 期望结果   │
├─────────────────────────────────────┤
│ 方向控制   │ 改变AIN1/2  │ 正反转正常 │
│ 速度控制   │ 调整PWM占空比│ 速度线性变化│
│ 制动功能   │ PWM=0      │ 电机停止   │
│ 同步性     │ 双电机对比  │ 速度一致   │
└─────────────────────────────────────┘
```

### 测试阶段3：系统集成验证

#### 循迹功能测试
```
制作测试轨道：
┌─────────────────────────────────────┐
│ • 使用白色A4纸作为底板              │
│ • 用黑色胶带贴出1.8cm宽的线条       │
│ • 制作简单的直线、弯道、十字路口     │
│ • 确保线条对比度清晰               │
└─────────────────────────────────────┘

测试步骤：
1. 将小车放在起始线上
2. 观察传感器数据变化
3. 验证线位置计算准确性
4. 测试电机差速控制
5. 记录循迹效果和稳定性
```

## 📋 故障排除指南

### 常见问题及解决方案

#### 问题1：传感器无数据
```
现象：串口显示传感器数据全为0或异常值
可能原因：
├── 供电问题：检查5V电源是否正常
├── 接线错误：核对AD0、AD1、AD2、OUT引脚
├── 使能信号：确认EN引脚为低电平
└── ADC配置：检查A27引脚ADC功能

解决步骤：
1. 用万用表测量传感器VCC = 5V
2. 检查所有信号线连接
3. 确认EN引脚输出低电平
4. 重新下载程序
```

#### 问题2：电机不转动
```
现象：电机无响应或只有一个电机转动
可能原因：
├── 供电不足：12V电源电流不够
├── 驱动故障：TB6612模块损坏
├── 接线错误：控制信号或电机线接错
└── 程序问题：PWM输出异常

解决步骤：
1. 检查VM引脚12V电压
2. 测量控制信号电平
3. 用示波器观察PWM波形
4. 更换电机或驱动模块测试
```

#### 问题3：系统重启或死机
```
现象：程序运行不稳定，频繁重启
可能原因：
├── 电源纹波：转换模块质量差
├── 电磁干扰：电机干扰主控电路
├── 电流冲击：电机启动瞬间电压跌落
└── 程序错误：死循环或栈溢出

解决步骤：
1. 在电源输入端加大容量滤波电容
2. 电机电源与逻辑电源分离
3. 添加电源监控和复位电路
4. 检查程序逻辑，添加看门狗
```

## 📞 技术支持

### 联系信息
```
项目技术支持：
┌─────────────────────────────────────┐
│ 技术文档：project/code/目录下        │
│ 接线图：  详细接线报告_12V供电方案.md │
│ 故障排除：快速故障排除.md           │
│ 官方手册：官方手册接线指南.md        │
└─────────────────────────────────────┘
```

### 版本信息
```
文档版本：v1.0
创建日期：2025-01-02
适用硬件：MSPM0G3507 + Ganwei 8路灰度传感器
适用软件：SeekFree库 v3.0.8 + Keil MDK 5.37
```

---

## ✅ 接线检查清单

### 最终验证清单
```
□ 12V电源连接正确，极性无误
□ 5V和3.3V输出电压正常
□ 主控板供电正常，调试串口工作
□ 8路灰度传感器5V供电，信号线连接正确
□ TB6612驱动模块双电源供电正常
□ 电机连接正确，方向和速度可控
□ 按键和LED功能正常
□ 系统整体工作稳定，无异常重启
□ 传感器数据正常，循迹功能可用
□ 所有连接牢固，无虚接现象
```

**🎉 完成以上检查后，您的8路灰度传感器循迹小车就可以正常工作了！**
