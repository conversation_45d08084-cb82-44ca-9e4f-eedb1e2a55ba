#include "pid.h"
float PID_Increase(PID *SPID,float Expectant,float Actual_value)
{
    register float Error,Output;                                                         
    Error = Expectant - Actual_value;                                                 
    Output = ((SPID->P + SPID->I + SPID->D) * Error     
           -(SPID->P + SPID->D * 2             ) * SPID->LastError    
            +(SPID->D                                   ) * SPID->PrevError);  
    SPID->PrevError = SPID->LastError;                                     
    SPID->LastError = Error;
    return(Output);                                                                 
}

float PID_Realize(PID *SPID,float Expectant,float Actual_value)
{
    register float Error,dError,place;
    Error = Expectant - Actual_value;                
    SPID->SumError += Error;    
    dError = Error - SPID->LastError;        
    SPID->LastError = Error;
    place = (SPID->P * Error     
                + SPID->I * SPID->SumError        
                       + SPID->D * dError);           
    return(place);
}
