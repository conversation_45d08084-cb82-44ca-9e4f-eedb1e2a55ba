/*********************************************************************************************************************
* 竞赛巡线主程序 - 100cm×100cm正方形赛道
* 题目要求：黑线宽1.8cm±0.2cm，逆时针行驶，1-5圈可设定，20秒内完成
*
* 硬件配置：
* - 感为8路灰度传感器：地址线A0/A1/A2，ADC输入A27
* - MG513X编码电机 + TB6612驱动
* - 按键：A10/A11/A12/A13
* - LED：B0/B1
*
* 按键功能：
* KEY1(A10) - 参数选择    KEY2(A11) - 参数+
* KEY3(A12) - 参数-      KEY4(A13) - 启动/停止
********************************************************************************************************************/

#include "zf_common_headfile.h"
#include "zf_device_ganwei_grayscale.h"

// ========================= 硬件定义 =========================
#define KEY1_PIN    A10     // 参数选择键
#define KEY2_PIN    A11     // 参数+键
#define KEY3_PIN    A12     // 参数-键
#define KEY4_PIN    A13     // 启动/停止键
#define LED1_PIN    B0      // 状态LED
#define LED2_PIN    B1      // 圈数LED

// ========================= 系统参数 =========================
uint8 target_laps = 3;          // 目标圈数(1-5)
uint16 base_speed = 500;        // 直道速度(100-800)
uint16 turn_speed = 250;        // 转弯速度(50-400)
uint8 sensitivity = 6;          // 转向灵敏度(1-10)

// ========================= 系统状态 =========================
uint8 system_state = 0;         // 0停止 1设置 2准备 3运行 4完成
uint8 param_mode = 0;           // 参数模式 0圈数 1直道速度 2转弯速度 3灵敏度
uint8 current_laps = 0;         // 当前圈数
uint32 start_time = 0;          // 开始时间
uint32 system_time = 0;         // 系统时间(ms)

// ========================= 传感器数据 =========================
ganwei_grayscale_info_struct grayscale_sensor;
uint16 gray_data[8] = {0};      // 8路传感器数据
uint8 line_position = 4;        // 线位置(0-7)
uint8 line_detected = 0;        // 检测到线标志
uint8 corner_detected = 0;      // 检测到拐角标志

// ========================= 按键处理 =========================
uint8 key_state[4] = {1,1,1,1}; // 按键当前状态
uint8 key_last[4] = {1,1,1,1};  // 按键上次状态

int main (void)
{
    clock_init(SYSTEM_CLOCK_80M);   // 时钟配置及系统初始化
    debug_init();					// 调试串口信息初始化

    debug_printf("=== 竞赛巡线系统启动 ===\r\n");

    // ==================== 硬件初始化 ====================
    // 按键初始化
    gpio_init(KEY1_PIN, GPI, GPIO_HIGH, GPI_PULL_UP);
    gpio_init(KEY2_PIN, GPI, GPIO_HIGH, GPI_PULL_UP);
    gpio_init(KEY3_PIN, GPI, GPIO_HIGH, GPI_PULL_UP);
    gpio_init(KEY4_PIN, GPI, GPIO_HIGH, GPI_PULL_UP);

    // LED初始化
    gpio_init(LED1_PIN, GPO, GPIO_LOW, GPO_PUSH_PULL);
    gpio_init(LED2_PIN, GPO, GPIO_LOW, GPO_PUSH_PULL);

    // 8路灰度传感器初始化
    ganwei_grayscale_init(&grayscale_sensor, GANWEI_GRAYSCALE_CLASS_EDITION,
                         GANWEI_GRAYSCALE_ADC_12BITS, A0, A1, A2, ADC0_CH0_A27);

    // 电机初始化
    gpio_init(A8, GPO, GPIO_HIGH, GPO_PUSH_PULL);   // 左电机方向1
    gpio_init(A9, GPO, GPIO_HIGH, GPO_PUSH_PULL);   // 左电机方向2
    gpio_init(B24, GPO, GPIO_HIGH, GPO_PUSH_PULL);  // 右电机方向1
    gpio_init(B25, GPO, GPIO_HIGH, GPO_PUSH_PULL);  // 右电机方向2
    pwm_init(PWM_TIM_A0_CH1_A7, 17000, 0);          // 左电机PWM
    pwm_init(PWM_TIM_A0_CH3_B26, 17000, 0);         // 右电机PWM

    debug_printf("硬件初始化完成\r\n");
    debug_printf("KEY1-参数选择 KEY2-参数+ KEY3-参数- KEY4-启动停止\r\n");

    while(true)
    {
        system_time++;

        // ==================== 按键扫描 ====================
        key_state[0] = gpio_get_level(KEY1_PIN);
        key_state[1] = gpio_get_level(KEY2_PIN);
        key_state[2] = gpio_get_level(KEY3_PIN);
        key_state[3] = gpio_get_level(KEY4_PIN);

        // KEY1 - 参数选择
        if(key_last[0] == 1 && key_state[0] == 0)
        {
            if(system_state == 0) system_state = 1; // 进入设置模式
            else if(system_state == 1) param_mode = (param_mode + 1) % 4; // 切换参数
            debug_printf("参数模式: %d\r\n", param_mode);
        }

        // KEY2 - 参数增加
        if(key_last[1] == 1 && key_state[1] == 0 && system_state == 1)
        {
            if(param_mode == 0 && target_laps < 5) target_laps++;
            else if(param_mode == 1 && base_speed < 800) base_speed += 50;
            else if(param_mode == 2 && turn_speed < 400) turn_speed += 25;
            else if(param_mode == 3 && sensitivity < 10) sensitivity++;
            debug_printf("圈数:%d 直道:%d 转弯:%d 灵敏度:%d\r\n", target_laps, base_speed, turn_speed, sensitivity);
        }

        // KEY3 - 参数减少
        if(key_last[2] == 1 && key_state[2] == 0 && system_state == 1)
        {
            if(param_mode == 0 && target_laps > 1) target_laps--;
            else if(param_mode == 1 && base_speed > 100) base_speed -= 50;
            else if(param_mode == 2 && turn_speed > 50) turn_speed -= 25;
            else if(param_mode == 3 && sensitivity > 1) sensitivity--;
            debug_printf("圈数:%d 直道:%d 转弯:%d 灵敏度:%d\r\n", target_laps, base_speed, turn_speed, sensitivity);
        }

        // KEY4 - 启动/停止
        if(key_last[3] == 1 && key_state[3] == 0)
        {
            if(system_state == 1) { system_state = 2; current_laps = 0; } // 设置->准备
            else if(system_state == 2) { system_state = 3; start_time = system_time; } // 准备->运行
            else if(system_state == 3) { system_state = 0; pwm_set_duty(PWM_TIM_A0_CH1_A7, 0); pwm_set_duty(PWM_TIM_A0_CH3_B26, 0); } // 运行->停止
            else if(system_state == 4) { system_state = 0; current_laps = 0; } // 完成->停止
            debug_printf("系统状态: %d\r\n", system_state);
        }

        // 更新按键历史
        for(uint8 i = 0; i < 4; i++) key_last[i] = key_state[i];

        // ==================== LED显示 ====================
        if(system_state == 0) { gpio_set_level(LED1_PIN, 0); gpio_set_level(LED2_PIN, 0); } // 停止
        else if(system_state == 1) { gpio_set_level(LED1_PIN, (system_time/100)%2); gpio_set_level(LED2_PIN, 0); } // 设置
        else if(system_state == 2) { gpio_set_level(LED1_PIN, 1); gpio_set_level(LED2_PIN, 0); } // 准备
        else if(system_state == 3) { gpio_set_level(LED1_PIN, 1); gpio_set_level(LED2_PIN, (system_time/200)%2); } // 运行
        else if(system_state == 4) { gpio_set_level(LED1_PIN, (system_time/150)%2); gpio_set_level(LED2_PIN, !((system_time/150)%2)); } // 完成

