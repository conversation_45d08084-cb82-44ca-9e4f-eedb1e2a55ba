#ifndef _simple_line_follow_h
#define _simple_line_follow_h

#include "zf_common_headfile.h"
#include "search.h"
#include "motor.h"

// 按键定义
#define KEY1_PIN    A10     // 功能选择键
#define KEY2_PIN    A11     // 参数调整键+
#define KEY3_PIN    A12     // 参数调整键-
#define KEY4_PIN    A13     // 启动/停止键

// LED指示灯定义
#define LED1_PIN    B0      // 状态指示LED
#define LED2_PIN    B1      // 圈数指示LED

// 系统状态枚举
typedef enum {
    STATE_STOP = 0,         // 停止状态
    STATE_READY,            // 准备状态
    STATE_RUNNING,          // 运行状态
    STATE_FINISHED,         // 完成状态
    STATE_SETTING           // 参数设置状态
} system_state_enum;

// 参数设置模式枚举
typedef enum {
    PARAM_LAP_COUNT = 0,    // 圈数设置
    PARAM_BASE_SPEED,       // 基础速度
    PARAM_TURN_SPEED,       // 转弯速度
    PARAM_SENSITIVITY,      // 灵敏度
    PARAM_MAX               // 参数总数
} param_mode_enum;

// 巡线参数结构体
typedef struct {
    uint8 target_laps;      // 目标圈数 (1-5)
    uint16 base_speed;      // 基础速度 (0-1000)
    uint16 turn_speed;      // 转弯速度 (0-1000)
    uint8 sensitivity;      // 转向灵敏度 (1-10)
    uint8 current_laps;     // 当前圈数
    system_state_enum state; // 系统状态
    param_mode_enum param_mode; // 参数模式
} line_follow_params_t;

// 巡线控制结构体
typedef struct {
    uint8 line_position;    // 线位置 (0-7)
    uint8 line_detected;    // 是否检测到线
    uint8 corner_detected;  // 是否检测到拐角
    uint8 start_detected;   // 是否检测到起点
    int16 motor_left;       // 左电机速度
    int16 motor_right;      // 右电机速度
} line_control_t;

// 函数声明
void Simple_Line_Follow_Init(void);         // 系统初始化
void Key_Scan(void);                        // 按键扫描
void LED_Display(void);                     // LED显示
void Parameter_Setting(void);               // 参数设置
void Simple_Line_Detection(void);           // 简单线检测
void Simple_Motor_Control(void);            // 简单电机控制
void Lap_Counter(void);                     // 圈数计数
void System_State_Machine(void);            // 状态机
void Display_Parameters(void);              // 参数显示

// 外部变量
extern line_follow_params_t line_params;
extern line_control_t line_control;

#endif
