/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : C:\Users\<USER>\Desktop\HOPE\project\keil\SeekFree_MSPM0G3507_Device_Library_Sequences_0000.log
 *  Created     : 16:06:18 (02/08/2025)
 *  Device      : MSPM0G3507
 *  PDSC File   : C:/keil5/TexasInstruments/MSPM0G1X0X_G3X0X_DFP/1.3.1_tmp/TexasInstruments.MSPM0G1X0X_G3X0X_DFP.pdsc
 *
 */

[16:06:18.241]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[16:06:18.241]  
[16:06:18.241]  <debugvars>
[16:06:18.242]    // Pre-defined
[16:06:18.242]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:06:18.242]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:06:18.242]    __dp=0x00000000
[16:06:18.242]    __ap=0x00000000
[16:06:18.243]    __traceout=0x00000000      (Trace Disabled)
[16:06:18.243]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:06:18.243]    __FlashAddr=0x00000000
[16:06:18.243]    __FlashLen=0x00000000
[16:06:18.243]    __FlashArg=0x00000000
[16:06:18.243]    __FlashOp=0x00000000
[16:06:18.243]    __Result=0x00000000
[16:06:18.243]  </debugvars>
[16:06:18.243]  
[16:06:18.243]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[16:06:18.243]    <block atomic="false" info="">
[16:06:18.243]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[16:06:18.244]        // -> [isSWJ <= 0x00000001]
[16:06:18.244]      __var hasDormant = __protocol & 0x00020000;
[16:06:18.244]        // -> [hasDormant <= 0x00000000]
[16:06:18.244]      __var protType   = __protocol & 0x0000FFFF;
[16:06:18.244]        // -> [protType <= 0x00000002]
[16:06:18.244]    </block>
[16:06:18.244]    <control if="protType == 1" while="" timeout="0" info="">
[16:06:18.244]      // if-block "protType == 1"
[16:06:18.244]        // =>  FALSE
[16:06:18.244]      // skip if-block "protType == 1"
[16:06:18.244]    </control>
[16:06:18.245]    <control if="protType == 2" while="" timeout="0" info="">
[16:06:18.245]      // if-block "protType == 2"
[16:06:18.245]        // =>  TRUE
[16:06:18.245]      <control if="isSWJ" while="" timeout="0" info="">
[16:06:18.245]        // if-block "isSWJ"
[16:06:18.245]          // =>  TRUE
[16:06:18.245]        <control if="hasDormant" while="" timeout="0" info="">
[16:06:18.245]          // if-block "hasDormant"
[16:06:18.245]            // =>  FALSE
[16:06:18.245]          // skip if-block "hasDormant"
[16:06:18.245]        </control>
[16:06:18.245]        <control if="!hasDormant" while="" timeout="0" info="">
[16:06:18.246]          // if-block "!hasDormant"
[16:06:18.246]            // =>  TRUE
[16:06:18.246]          <block atomic="false" info="">
[16:06:18.246]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[16:06:18.247]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[16:06:18.247]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[16:06:18.249]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[16:06:18.249]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[16:06:18.251]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[16:06:18.252]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[16:06:18.254]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[16:06:18.254]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[16:06:18.257]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[16:06:18.257]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[16:06:18.260]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[16:06:18.260]          </block>
[16:06:18.260]          // end if-block "!hasDormant"
[16:06:18.260]        </control>
[16:06:18.260]        // end if-block "isSWJ"
[16:06:18.260]      </control>
[16:06:18.260]      <control if="!isSWJ" while="" timeout="0" info="">
[16:06:18.260]        // if-block "!isSWJ"
[16:06:18.260]          // =>  FALSE
[16:06:18.260]        // skip if-block "!isSWJ"
[16:06:18.260]      </control>
[16:06:18.260]      <block atomic="false" info="">
[16:06:18.260]        ReadDP(0x0);
[16:06:18.262]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[16:06:18.262]      </block>
[16:06:18.262]      // end if-block "protType == 2"
[16:06:18.262]    </control>
[16:06:18.262]  </sequence>
[16:06:18.262]  
[16:06:18.266]  **********  Sequence "DebugPortStart"  (Context="Connect", Pname="", info="")
[16:06:18.266]  
[16:06:18.266]  <debugvars>
[16:06:18.266]    // Pre-defined
[16:06:18.266]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:06:18.266]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:06:18.267]    __dp=0x00000000
[16:06:18.267]    __ap=0x00000000
[16:06:18.267]    __traceout=0x00000000      (Trace Disabled)
[16:06:18.267]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:06:18.267]    __FlashAddr=0x00000000
[16:06:18.267]    __FlashLen=0x00000000
[16:06:18.267]    __FlashArg=0x00000000
[16:06:18.268]    __FlashOp=0x00000000
[16:06:18.268]    __Result=0x00000000
[16:06:18.268]  </debugvars>
[16:06:18.268]  
[16:06:18.268]  <sequence name="DebugPortStart" Pname="" disable="false" info="">
[16:06:18.268]    <block atomic="false" info="">
[16:06:18.268]      __var SW_DP_ABORT       = 0x0;
[16:06:18.268]        // -> [SW_DP_ABORT <= 0x00000000]
[16:06:18.268]      __var DP_CTRL_STAT      = 0x4;
[16:06:18.269]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:06:18.274]      __var DP_SELECT         = 0x8;
[16:06:18.275]        // -> [DP_SELECT <= 0x00000008]
[16:06:18.275]      __var powered_down      = 0;
[16:06:18.275]        // -> [powered_down <= 0x00000000]
[16:06:18.275]      WriteDP(DP_SELECT, 0x00000000);
[16:06:18.277]        // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:06:18.278]      powered_down = ((ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000);
[16:06:18.279]        // -> [ReadDP(0x00000004) => 0x00000040]   (__dp=0x00000000)
[16:06:18.279]        // -> [powered_down <= 0x00000001]
[16:06:18.280]    </block>
[16:06:18.280]    <control if="powered_down" while="" timeout="0" info="">
[16:06:18.280]      // if-block "powered_down"
[16:06:18.280]        // =>  TRUE
[16:06:18.280]      <block atomic="false" info="">
[16:06:18.280]        Message(0, "Debug/System power-up request sent");
[16:06:18.281]        WriteDP(DP_CTRL_STAT, 0x50000000);
[16:06:18.283]          // -> [WriteDP(0x00000004, 0x50000000)]   (__dp=0x00000000)
[16:06:18.283]      </block>
[16:06:18.283]      <control if="" while="(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000" timeout="1000000" info="">
[16:06:18.283]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[16:06:18.285]          // -> [ReadDP(0x00000004) => 0xF0000040]   (__dp=0x00000000)
[16:06:18.285]        // while-condition  =>  FALSE
[16:06:18.285]        // end while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"
[16:06:18.285]      </control>
[16:06:18.285]      <control if="(__protocol & 0xFFFF) == 1" while="" timeout="0" info="">
[16:06:18.285]        // if-block "(__protocol & 0xFFFF) == 1"
[16:06:18.285]          // =>  FALSE
[16:06:18.285]        // skip if-block "(__protocol & 0xFFFF) == 1"
[16:06:18.285]      </control>
[16:06:18.285]      <control if="(__protocol & 0xFFFF) == 2" while="" timeout="0" info="">
[16:06:18.285]        // if-block "(__protocol & 0xFFFF) == 2"
[16:06:18.286]          // =>  TRUE
[16:06:18.286]        <block atomic="false" info="">
[16:06:18.286]          Message(0, "executing SWD power up");
[16:06:18.288]          WriteDP(DP_CTRL_STAT, 0x50000F00);
[16:06:18.291]            // -> [WriteDP(0x00000004, 0x50000F00)]   (__dp=0x00000000)
[16:06:18.291]          WriteDP(SW_DP_ABORT, 0x0000001E);
[16:06:18.293]            // -> [WriteDP(0x00000000, 0x0000001E)]   (__dp=0x00000000)
[16:06:18.293]        </block>
[16:06:18.293]        // end if-block "(__protocol & 0xFFFF) == 2"
[16:06:18.293]      </control>
[16:06:18.293]      // end if-block "powered_down"
[16:06:18.293]    </control>
[16:06:18.293]    <block atomic="false" info="">
[16:06:18.293]      __var DEBUG_PORT_VAL    = 0;
[16:06:18.294]        // -> [DEBUG_PORT_VAL <= 0x00000000]
[16:06:18.294]      __var ACCESS_POINT_VAL  = 0;
[16:06:18.294]        // -> [ACCESS_POINT_VAL <= 0x00000000]
[16:06:18.294]      __ap = 1; 
[16:06:18.294]        // -> [__ap <= 0x00000001]
[16:06:18.294]      WriteAP(0x0C, 0x04); //lets use the mini pwr-ap as backup
[16:06:18.298]        // -> [WriteAP(0x0000000C, 0x00000004)]   (__dp=0x00000000, __ap=0x00000001)
[16:06:18.298]      __ap = 4;
[16:06:18.298]        // -> [__ap <= 0x00000004]
[16:06:18.298]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[16:06:18.302]        // -> [ReadAP(0x00000000) => 0x00080027]   (__dp=0x00000000, __ap=0x00000004)
[16:06:18.302]        // -> [ACCESS_POINT_VAL <= 0x00080027]
[16:06:18.302]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[16:06:18.303]    </block>
[16:06:18.304]    <block atomic="false" info="">
[16:06:18.304]      __var nReset = 0x80;
[16:06:18.304]        // -> [nReset <= 0x00000080]
[16:06:18.304]      __var canReadPins = 0;
[16:06:18.304]        // -> [canReadPins <= 0x00000000]
[16:06:18.304]      canReadPins = (DAP_SWJ_Pins(0x00, nReset, 0) != 0xFFFFFFFF);
[16:06:18.306]        // -> [DAP_SWJ_Pins(0x00, 0x80, 0) => 0x02]   (Out: nRESET=0  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[16:06:18.306]        // -> [canReadPins <= 0x00000001]
[16:06:18.306]    </block>
[16:06:18.306]    <control if="" while="1" timeout="200" info="">
[16:06:18.306]      // while "1"  (timeout="200")
[16:06:18.306]      // while-condition  =>  TRUE
[16:06:18.306]      // while "1"  (timeout="200")
[16:06:18.306]      // while-condition  =>  TRUE
[16:06:18.307]      // while "1"  (timeout="200")
[16:06:18.307]      // while-condition  =>  TRUE
[16:06:18.307]      // while "1"  (timeout="200")
[16:06:18.307]      // while-condition  =>  TRUE
[16:06:18.307]      // while "1"  (timeout="200")
[16:06:18.307]      // while-condition  =>  TRUE
[16:06:18.307]      // while "1"  (timeout="200")
[16:06:18.307]      // while-condition  =>  TRUE
[16:06:18.307]      // while "1"  (timeout="200")
[16:06:18.307]      // while-condition  =>  TRUE
[16:06:18.307]      // while "1"  (timeout="200")
[16:06:18.307]      // while-condition  =>  TRUE
[16:06:18.307]      // while "1"  (timeout="200")
[16:06:18.307]      // while-condition  =>  TRUE
[16:06:18.308]      // while "1"  (timeout="200")
[16:06:18.308]      // while-condition  =>  TRUE
[16:06:18.308]      // while "1"  (timeout="200")
[16:06:18.308]      // while-condition  =>  TRUE
[16:06:18.308]      // while "1"  (timeout="200")
[16:06:18.308]      // while-condition  =>  TRUE
[16:06:18.308]      // while "1"  (timeout="200")
[16:06:18.308]      // while-condition  =>  TRUE
[16:06:18.308]      // while "1"  (timeout="200")
[16:06:18.308]      // while-condition  =>  TRUE
[16:06:18.308]      // while "1"  (timeout="200")
[16:06:18.308]      // while-condition  =>  TRUE
[16:06:18.308]      // while "1"  (timeout="200")
[16:06:18.308]      // while-condition  =>  TRUE
[16:06:18.309]      // while "1"  (timeout="200")
[16:06:18.309]      // while-condition  =>  TRUE
[16:06:18.309]      // while "1"  (timeout="200")
[16:06:18.309]      // while-condition  =>  TRUE
[16:06:18.309]      // while "1"  (timeout="200")
[16:06:18.309]      // while-condition  =>  TRUE
[16:06:18.309]      // while "1"  (timeout="200")
[16:06:18.309]      // while-condition  =>  TRUE
[16:06:18.309]      // while "1"  (timeout="200")
[16:06:18.309]      // while-condition  =>  TRUE
[16:06:18.309]      // while "1"  (timeout="200")
[16:06:18.309]      // while-condition  =>  TRUE
[16:06:18.309]      // while "1"  (timeout="200")
[16:06:18.309]      // while-condition  =>  TRUE
[16:06:18.310]      // while "1"  (timeout="200")
[16:06:18.310]      // while-condition  =>  TRUE
[16:06:18.310]      // while "1"  (timeout="200")
[16:06:18.310]      // while-condition  =>  TRUE
[16:06:18.310]      // while "1"  (timeout="200")
[16:06:18.310]      // while-condition  =>  TRUE
[16:06:18.310]      // while "1"  (timeout="200")
[16:06:18.310]      // while-condition  =>  TRUE
[16:06:18.310]      // while "1"  (timeout="200")
[16:06:18.310]      // while-condition  =>  TRUE
[16:06:18.311]      // while "1"  (timeout="200")
[16:06:18.311]      // while-condition  =>  TRUE
[16:06:18.311]      // while "1"  (timeout="200")
[16:06:18.311]      // while-condition  =>  TRUE
[16:06:18.311]      // while "1"  (timeout="200")
[16:06:18.311]      // while-condition  =>  TRUE
[16:06:18.311]      // while "1"  (timeout="200")
[16:06:18.311]      // while-condition  =>  TRUE
[16:06:18.312]      // while "1"  (timeout="200")
[16:06:18.312]      // while-condition  =>  TRUE
[16:06:18.312]      // while "1"  (timeout="200")
[16:06:18.312]      // while-condition  =>  TRUE
[16:06:18.312]      // while "1"  (timeout="200")
[16:06:18.312]      // while-condition  =>  TRUE
[16:06:18.312]      // while "1"  (timeout="200")
[16:06:18.312]      // while-condition  =>  TRUE
[16:06:18.312]      // while "1"  (timeout="200")
[16:06:18.312]      // while-condition  =>  TRUE
[16:06:18.312]      // while "1"  (timeout="200")
[16:06:18.312]      // while-condition  =>  TRUE
[16:06:18.312]      // while "1"  (timeout="200")
[16:06:18.313]      // while-condition  =>  TRUE
[16:06:18.313]      // while "1"  (timeout="200")
[16:06:18.313]      // while-condition  =>  TRUE
[16:06:18.313]      // while "1"  (timeout="200")
[16:06:18.313]      // while-condition  =>  TRUE
[16:06:18.313]      // while "1"  (timeout="200")
[16:06:18.313]      // while-condition  =>  TRUE
[16:06:18.313]      // while "1"  (timeout="200")
[16:06:18.313]      // while-condition  =>  TRUE
[16:06:18.314]      // while "1"  (timeout="200")
[16:06:18.314]      // while  =>  TIMEOUT
[16:06:18.314]      // end while "1"
[16:06:18.314]    </control>
[16:06:18.314]    <control if="canReadPins" while="" timeout="0" info="">
[16:06:18.314]      // if-block "canReadPins"
[16:06:18.314]        // =>  TRUE
[16:06:18.314]      <control if="" while="(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0" timeout="1000000" info="">
[16:06:18.314]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[16:06:18.316]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x02]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[16:06:18.316]        // while-condition  =>  TRUE
[16:06:18.316]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[16:06:18.318]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x82]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=1)
[16:06:18.318]        // while-condition  =>  FALSE
[16:06:18.318]        // end while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"
[16:06:18.318]      </control>
[16:06:18.319]      // end if-block "canReadPins"
[16:06:18.319]    </control>
[16:06:18.319]    <control if="!canReadPins" while="" timeout="0" info="">
[16:06:18.319]      // if-block "!canReadPins"
[16:06:18.319]        // =>  FALSE
[16:06:18.319]      // skip if-block "!canReadPins"
[16:06:18.319]    </control>
[16:06:18.319]    <control if="(ACCESS_POINT_VAL & 0x00E00000) == 0" while="" timeout="0" info="">
[16:06:18.319]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[16:06:18.319]        // =>  TRUE
[16:06:18.319]      <block atomic="false" info="">
[16:06:18.319]        WriteAP(0x00, 0x190008);
[16:06:18.322]          // -> [WriteAP(0x00000000, 0x00190008)]   (__dp=0x00000000, __ap=0x00000004)
[16:06:18.323]        WriteAP(0xF0, 0x01);
[16:06:18.326]          // -> [WriteAP(0x000000F0, 0x00000001)]   (__dp=0x00000000, __ap=0x00000004)
[16:06:18.326]      </block>
[16:06:18.327]      // end if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[16:06:18.327]    </control>
[16:06:18.327]    <control if="(ACCESS_POINT_VAL & 0x00E00000) != 0" while="" timeout="0" info="">
[16:06:18.327]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[16:06:18.327]        // =>  FALSE
[16:06:18.327]      // skip if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[16:06:18.327]    </control>
[16:06:18.327]    <block atomic="false" info="">
[16:06:18.327]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[16:06:18.330]        // -> [ReadAP(0x00000000) => 0x0079002F]   (__dp=0x00000000, __ap=0x00000004)
[16:06:18.332]        // -> [ACCESS_POINT_VAL <= 0x0079002F]
[16:06:18.332]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[16:06:18.333]      __ap = 0; //lets make sure we reset the access point selection
[16:06:18.333]        // -> [__ap <= 0x00000000]
[16:06:18.333]    </block>
[16:06:18.333]  </sequence>
[16:06:18.333]  
[16:06:18.353]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:06:18.353]  
[16:06:18.353]  <debugvars>
[16:06:18.353]    // Pre-defined
[16:06:18.353]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:06:18.353]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:06:18.353]    __dp=0x00000000
[16:06:18.353]    __ap=0x00000000
[16:06:18.353]    __traceout=0x00000000      (Trace Disabled)
[16:06:18.353]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:06:18.353]    __FlashAddr=0x00000000
[16:06:18.354]    __FlashLen=0x00000000
[16:06:18.354]    __FlashArg=0x00000000
[16:06:18.354]    __FlashOp=0x00000000
[16:06:18.354]    __Result=0x00000000
[16:06:18.354]  </debugvars>
[16:06:18.354]  
[16:06:18.354]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:06:18.354]    <block atomic="false" info="">
[16:06:18.354]      __var deviceID = 0;
[16:06:18.354]        // -> [deviceID <= 0x00000000]
[16:06:18.354]      __var version = 0;
[16:06:18.354]        // -> [version <= 0x00000000]
[16:06:18.354]      __var partNum = 0;
[16:06:18.355]        // -> [partNum <= 0x00000000]
[16:06:18.355]      __var manuf = 0;
[16:06:18.355]        // -> [manuf <= 0x00000000]
[16:06:18.355]      __var isMSPM0G1X0X_G3X0X = 0;
[16:06:18.355]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000000]
[16:06:18.355]      __var isProduction = 0;
[16:06:18.357]        // -> [isProduction <= 0x00000000]
[16:06:18.357]      __var continueId = 0;
[16:06:18.357]        // -> [continueId <= 0x00000000]
[16:06:18.357]      deviceID =   Read32(0x41C40004);
[16:06:18.364]        // -> [Read32(0x41C40004) => 0x2BB8802F]   (__dp=0x00000000, __ap=0x00000000)
[16:06:18.364]        // -> [deviceID <= 0x2BB8802F]
[16:06:18.364]      version = deviceID >> 28;
[16:06:18.364]        // -> [version <= 0x00000002]
[16:06:18.364]      partNum = (deviceID & 0x0FFFF000) >> 12;
[16:06:18.364]        // -> [partNum <= 0x0000BB88]
[16:06:18.364]      manuf = (deviceID & 0x00000FFE) >> 1;
[16:06:18.364]        // -> [manuf <= 0x00000017]
[16:06:18.364]      isMSPM0G1X0X_G3X0X = (partNum == 0xBB88) && (manuf == 0x17);
[16:06:18.364]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000001]
[16:06:18.364]      isProduction = (version > 0);
[16:06:18.364]        // -> [isProduction <= 0x00000001]
[16:06:18.364]    </block>
[16:06:18.364]    <control if="!isMSPM0G1X0X_G3X0X" while="" timeout="0" info="">
[16:06:18.364]      // if-block "!isMSPM0G1X0X_G3X0X"
[16:06:18.364]        // =>  FALSE
[16:06:18.364]      // skip if-block "!isMSPM0G1X0X_G3X0X"
[16:06:18.365]    </control>
[16:06:18.365]    <control if="continueId == 4" while="" timeout="0" info="">
[16:06:18.365]      // if-block "continueId == 4"
[16:06:18.365]        // =>  FALSE
[16:06:18.365]      // skip if-block "continueId == 4"
[16:06:18.365]    </control>
[16:06:18.365]    <control if="!isProduction" while="" timeout="0" info="">
[16:06:18.365]      // if-block "!isProduction"
[16:06:18.365]        // =>  FALSE
[16:06:18.365]      // skip if-block "!isProduction"
[16:06:18.365]    </control>
[16:06:18.366]  </sequence>
[16:06:18.366]  
