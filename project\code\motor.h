#ifndef __motor_h
#define __motor_h

#include "zf_common_headfile.h"
#include "pid.h"

// 电机配置枚举
typedef enum
{
    MOT_Frequency,        // 电机频率配置
    MOT_MinDuty,          // 最小占空比配置
    MOT_MaxDuty,          // 最大占空比配置
}Mot_CMD;

// 编码器数据结构
typedef struct {
    float Encoder_L;      // 左编码器值
    float Encoder_R;      // 右编码器值
} encoder_struct;

// 函数声明
void Motor_Init(void);                    // 电机初始化
void Motor_Control_Right(int32 dutyR);    // 右电机控制
void Motor_Control_Left(int32 dutyL);     // 左电机控制
void MotorPid_Right(float Vright);       // 右电机PID控制
void MotorPid_Left(float Vleft);         // 左电机PID控制
void Turn_Extern_PID(float MidErr);      // 转向PID控制
void Angle_PID(float Err);               // 角度PID控制

// 外部变量声明
extern encoder_struct encoder;           // 编码器数据
extern float Angle_yaw;                  // 偏航角度
extern float Turn_Extern_data;           // 转向控制量
extern int16 PWM_L, PWM_R;              // 左右电机PWM值

#endif