<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>SeekFree_MSPM0G3507_Device_Library</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6220000::V6.22::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>MSPM0G3507</Device>
          <Vendor>Texas Instruments</Vendor>
          <PackID>TexasInstruments.MSPM0G1X0X_G3X0X_DFP.1.3.1</PackID>
          <PackURL>https://software-dl.ti.com/msp430/esd/MSPM0-CMSIS/MSPM0G1X0X_G3X0X/latest/exports/</PackURL>
          <Cpu>IRAM(0x20000000,0x00008000) IRAM2(0x20200000,0x00008000) IROM(0x00000000,0x00020000) IROM2(0x00400000,0x00020000) CPUTYPE("Cortex-M0+") CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC8000 -FN1 -FF0MSPM0G_MainFlash_128KB -FS00 -FL020000 -FP0($$Device:MSPM0G3507$02_Flash_Programming\FlashARM\MSPM0G_MainFlash_128KB.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile></RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:MSPM0G3507$03_SVD\MSPM0G350x.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>SeekFree_MSPM0G3507_Device_Library</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>cmd.exe /C "$P../../../tools/keil/syscfg.bat '$P' SeekFree_MSPM0G3507_Device_Library.syscfg"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -MPU </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0+</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments>-MPU </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0+</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0+"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>1</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>4</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>0</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>0</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x20000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x400000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x20200000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>1</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>4</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>1</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>__MSPM0G3507__</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\libraries\sdk\third_party\CMSIS\Core\Include;..\..\libraries\sdk;..\..\libraries\sdk\ti_config;..\..\libraries\zf_common;..\..\libraries\zf_components;..\..\libraries\zf_device;..\..\libraries\zf_driver;..\user\inc;..\code</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\mspm0g3507.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>user</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\src\main.c</FilePath>
            </File>
            <File>
              <FileName>isr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\src\isr.c</FilePath>
            </File>
            <File>
              <FileName>isr.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\user\inc\isr.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>zf_common</GroupName>
          <Files>
            <File>
              <FileName>zf_common_clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_clock.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_clock.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_clock.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_debug.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_debug.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_debug.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_fifo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_fifo.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_fifo.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_fifo.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_font.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_font.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_font.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_font.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_function.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_function.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_function.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_function.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_headfile.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_headfile.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_interrupt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_interrupt.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_interrupt.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_interrupt.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_typedef.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_typedef.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>zf_components</GroupName>
          <Files>
            <File>
              <FileName>seekfree_assistant.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_components\seekfree_assistant.c</FilePath>
            </File>
            <File>
              <FileName>seekfree_assistant.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_components\seekfree_assistant.h</FilePath>
            </File>
            <File>
              <FileName>seekfree_assistant_interface.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_components\seekfree_assistant_interface.c</FilePath>
            </File>
            <File>
              <FileName>seekfree_assistant_interface.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_components\seekfree_assistant_interface.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>zf_driver</GroupName>
          <Files>
            <File>
              <FileName>zf_driver_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_adc.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_adc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_adc.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_delay.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_delay.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_delay.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_exti.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_exti.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_exti.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_gpio.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_gpio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_gpio.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_pit.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_pit.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_pit.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_pit.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_pwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_pwm.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_pwm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_pwm.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_soft_iic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_soft_iic.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_soft_iic.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_soft_iic.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_spi.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_spi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_spi.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_timer.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_timer.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_timer.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_uart.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_uart.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_flash.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_flash.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_flash.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>zf_device</GroupName>
          <Files>
            <File>
              <FileName>zf_device_absolute_encoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_absolute_encoder.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_absolute_encoder.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_absolute_encoder.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_config.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_config.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_config.lib</FilePath>
            </File>
            <File>
              <FileName>zf_device_dl1a.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_dl1a.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_dl1a.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_dl1a.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_dl1b.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_dl1b.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_dl1b.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_dl1b.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_imu660ra.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_imu660ra.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_imu660ra.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_imu660ra.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_imu660rb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_imu660rb.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_imu660rb.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_imu660rb.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_imu963ra.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_imu963ra.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_imu963ra.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_imu963ra.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_ips114.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ips114.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_ips114.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ips114.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_ips200.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ips200.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_ips200.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ips200.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_ips200pro.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ips200pro.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_ips200pro.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ips200pro.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_tft180.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_tft180.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_tft180.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_tft180.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_tsl1401.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_tsl1401.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_tsl1401.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_tsl1401.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_type.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_type.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_type.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_type.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_wifi_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_wifi_spi.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_wifi_spi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_wifi_spi.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_wifi_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_wifi_uart.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_wifi_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_wifi_uart.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_wireless_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_wireless_uart.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_wireless_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_wireless_uart.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_key.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_key.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_key.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_ganwei_grayscale.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ganwei_grayscale.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_ganwei_grayscale.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ganwei_grayscale.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>code</GroupName>
          <Files>
            <File>
              <FileName>motor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\code\motor.c</FilePath>
            </File>
            <File>
              <FileName>motor.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\code\motor.h</FilePath>
            </File>
            <File>
              <FileName>pid.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\code\pid.c</FilePath>
            </File>
            <File>
              <FileName>pid.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\code\pid.h</FilePath>
            </File>
            <File>
              <FileName>search.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\code\search.c</FilePath>
            </File>
            <File>
              <FileName>search.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\code\search.h</FilePath>
            </File>
            <File>
              <FileName>simple_line_follow.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\code\simple_line_follow.c</FilePath>
            </File>
            <File>
              <FileName>simple_line_follow.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\code\simple_line_follow.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ti_config</GroupName>
          <Files>
            <File>
              <FileName>startup_mspm0g350x_uvision.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\libraries\sdk\ti_config\startup_mspm0g350x_uvision.s</FilePath>
            </File>
            <File>
              <FileName>ti_msp_dl_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti_config\ti_msp_dl_config.c</FilePath>
            </File>
            <File>
              <FileName>ti_msp_dl_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\ti_config\ti_msp_dl_config.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Driverlib</GroupName>
          <Files>
            <File>
              <FileName>dl_adc12.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_adc12.c</FilePath>
            </File>
            <File>
              <FileName>dl_aes.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_aes.c</FilePath>
            </File>
            <File>
              <FileName>dl_aesadv.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_aesadv.c</FilePath>
            </File>
            <File>
              <FileName>dl_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_common.c</FilePath>
            </File>
            <File>
              <FileName>dl_comp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_comp.c</FilePath>
            </File>
            <File>
              <FileName>dl_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_crc.c</FilePath>
            </File>
            <File>
              <FileName>dl_crcp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_crcp.c</FilePath>
            </File>
            <File>
              <FileName>dl_dac12.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_dac12.c</FilePath>
            </File>
            <File>
              <FileName>dl_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_dma.c</FilePath>
            </File>
            <File>
              <FileName>dl_flashctl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_flashctl.c</FilePath>
            </File>
            <File>
              <FileName>dl_gpamp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_gpamp.c</FilePath>
            </File>
            <File>
              <FileName>dl_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_gpio.c</FilePath>
            </File>
            <File>
              <FileName>dl_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_i2c.c</FilePath>
            </File>
            <File>
              <FileName>dl_iwdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_iwdt.c</FilePath>
            </File>
            <File>
              <FileName>dl_keystorectl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_keystorectl.c</FilePath>
            </File>
            <File>
              <FileName>dl_lcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_lcd.c</FilePath>
            </File>
            <File>
              <FileName>dl_lfss.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_lfss.c</FilePath>
            </File>
            <File>
              <FileName>dl_mathacl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_mathacl.c</FilePath>
            </File>
            <File>
              <FileName>dl_mcan.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_mcan.c</FilePath>
            </File>
            <File>
              <FileName>dl_opa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_opa.c</FilePath>
            </File>
            <File>
              <FileName>dl_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_rtc.c</FilePath>
            </File>
            <File>
              <FileName>dl_rtc_a.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_rtc_a.c</FilePath>
            </File>
            <File>
              <FileName>dl_rtc_b.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_rtc_b.c</FilePath>
            </File>
            <File>
              <FileName>dl_rtc_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_rtc_common.c</FilePath>
            </File>
            <File>
              <FileName>dl_scratchpad.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_scratchpad.c</FilePath>
            </File>
            <File>
              <FileName>dl_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_spi.c</FilePath>
            </File>
            <File>
              <FileName>dl_tamperio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_tamperio.c</FilePath>
            </File>
            <File>
              <FileName>dl_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_timer.c</FilePath>
            </File>
            <File>
              <FileName>dl_timera.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_timera.c</FilePath>
            </File>
            <File>
              <FileName>dl_timerg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_timerg.c</FilePath>
            </File>
            <File>
              <FileName>dl_trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_trng.c</FilePath>
            </File>
            <File>
              <FileName>dl_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_uart.c</FilePath>
            </File>
            <File>
              <FileName>dl_vref.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_vref.c</FilePath>
            </File>
            <File>
              <FileName>dl_wwdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\ti\driverlib\dl_wwdt.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>empty_LP_MSPM0G3507_nortos_keil</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
