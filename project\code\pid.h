#ifndef __Pid_H_
#define __Pid_H_

#include "zf_common_headfile.h"

typedef struct PID
{
  float P;
  float I; 
  float D;
  float SumError;    
  float LastError;     
  float PrevError;     
} PID;

float PID_Increase(PID *SPID,float Expectant,float Actual_value);
float PID_Realize(PID *SPID,float Expectant,float Actual_value);
extern PID Motor_L_speed;
extern PID Motor_R_speed;
extern PID Turn_PID;
extern PID Ang_PID;
#endif 
