/*********************************************************************************************************************
* 8路灰度传感器循迹小车示例程序
* 使用感为8路无MCU灰度传感器替换原有5路ADC传感器
* 
* 硬件连接:
* 感为8路灰度传感器:
* - 地址线0: A0
* - 地址线1: A1  
* - 地址线2: A2
* - ADC输入: A27 (ADC0_CH0)
* 
* 电机驱动:
* - 左电机: A7(PWM), A8/A9(方向)
* - 右电机: B26(PWM), B24/B25(方向)
********************************************************************************************************************/

#include "zf_common_headfile.h"
#include "search.h"
#include "motor.h"
#include "pid.h"

// 系统状态标志
uint8 system_init_flag = 0;
uint8 calibration_flag = 0;

// 控制参数
float target_speed = 50.0f;              // 目标速度
float speed_left = 0.0f;                 // 左轮速度
float speed_right = 0.0f;                // 右轮速度

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     系统初始化
// 参数说明     void
// 返回参数     void
// 使用示例     System_Init();
// 备注信息     初始化所有硬件模块
//-------------------------------------------------------------------------------------------------------------------
void System_Init(void)
{
    // 系统时钟和调试初始化
    clock_init(SYSTEM_CLOCK_80M);
    debug_init();
    
    debug_printf("=== 8路灰度传感器循迹小车系统 ===\r\n");
    debug_printf("正在初始化硬件模块...\r\n");
    
    // 初始化8路灰度传感器
    Gray_init();
    if(sensor_init_flag)
    {
        debug_printf("8路灰度传感器初始化成功\r\n");
    }
    else
    {
        debug_printf("8路灰度传感器初始化失败\r\n");
        return;
    }
    
    // 初始化电机系统
    Motor_Init();
    debug_printf("电机系统初始化完成\r\n");
    
    // 延时等待系统稳定
    system_delay_ms(1000);
    
    system_init_flag = 1;
    debug_printf("系统初始化完成，开始循迹控制\r\n");
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     传感器校准
// 参数说明     void
// 返回参数     void
// 使用示例     Sensor_Calibration();
// 备注信息     在白纸和黑线上进行传感器校准
//-------------------------------------------------------------------------------------------------------------------
void Sensor_Calibration(void)
{
    debug_printf("开始传感器校准...\r\n");
    debug_printf("请将传感器放在白纸上，3秒后开始校准\r\n");
    
    system_delay_ms(3000);
    
    // 执行校准
    Gray_calibration();
    
    if(grayscale_sensor.init_flag)
    {
        calibration_flag = 1;
        debug_printf("传感器校准完成\r\n");
    }
    else
    {
        debug_printf("传感器校准失败\r\n");
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     循迹控制算法
// 参数说明     void
// 返回参数     void
// 使用示例     Line_Following_Control();
// 备注信息     基于8路传感器的循迹控制
//-------------------------------------------------------------------------------------------------------------------
void Line_Following_Control(void)
{
    // 获取传感器数据
    Get_gray();
    
    // 检查是否检测到直角
    if(angle_flag)
    {
        // 直角处理：停止或减速
        speed_left = target_speed * 0.3f;
        speed_right = target_speed * 0.3f;
        debug_printf("检测到直角，减速处理\r\n");
    }
    else
    {
        // 正常循迹：根据误差调整速度
        speed_left = target_speed;
        speed_right = target_speed;
        
        // 使用转向PID控制
        Turn_Extern_PID(Gray_error);
    }
    
    // 执行电机PID控制
    MotorPid_Left(speed_left);
    MotorPid_Right(speed_right);
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     数据监控和调试
// 参数说明     void
// 返回参数     void
// 使用示例     Debug_Monitor();
// 备注信息     定期输出传感器数据用于调试
//-------------------------------------------------------------------------------------------------------------------
void Debug_Monitor(void)
{
    static uint32 debug_counter = 0;
    
    debug_counter++;
    
    // 每100次循环输出一次调试信息
    if(debug_counter >= 100)
    {
        debug_counter = 0;
        
        debug_printf("传感器数据: ");
        for(uint8 i = 0; i < GRAYSCALE_CHANNEL_NUM; i++)
        {
            debug_printf("%d ", gray_analog[i]);
        }
        debug_printf("\r\n");
        
        debug_printf("循迹误差: %.2f, 直角标志: %d\r\n", Gray_error, angle_flag);
        debug_printf("电机PWM: L=%d, R=%d\r\n", PWM_L, PWM_R);
        debug_printf("转向控制量: %.2f\r\n", Turn_Extern_data);
        debug_printf("------------------------\r\n");
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     主函数
// 参数说明     void
// 返回参数     int
// 使用示例     main();
// 备注信息     系统主循环
//-------------------------------------------------------------------------------------------------------------------
int main(void)
{
    // 系统初始化
    System_Init();
    
    if(!system_init_flag)
    {
        debug_printf("系统初始化失败，程序停止\r\n");
        while(1)
        {
            system_delay_ms(1000);
        }
    }
    
    // 传感器校准（可选）
    // Sensor_Calibration();
    
    debug_printf("开始主循环控制\r\n");
    
    // 主控制循环
    while(1)
    {
        // 循迹控制
        Line_Following_Control();
        
        // 调试监控
        Debug_Monitor();
        
        // 控制周期延时（建议5-10ms）
        system_delay_ms(5);
    }
    
    return 0;
}
