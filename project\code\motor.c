#include "motor.h"
#include "pid.h"
// #include "servo.h"  // 暂时注释，如需要请添加servo.h文件
// PID控制器实例
PID Motor_L_speed={10,35,0,0,0,0};        // 左电机速度PID
PID Motor_R_speed={10,35,0,0,0,0};        // 右电机速度PID
PID Turn_PID={0.1,0,0.05,0,0,0};          // 转向PID
PID Ang_PID={1,0,0.5,0,0,0};              // 角度PID

// 电机控制变量
int16 PWM_L=0,PWM_R=0;                    // 左右电机PWM值
float Turn_Extern_data = 0;               // 转向控制量

// 编码器和角度数据（需要根据实际硬件配置）
encoder_struct encoder = {0.0f, 0.0f};   // 编码器数据结构
float Angle_yaw = 0.0f;                  // 偏航角度
int16 MOT_CFG[][2]=
{
    {MOT_Frequency,        17000},         //���Ƶ��
    {MOT_MinDuty,          -9900},         //�����Сռ�ձ�(-10000~10000),������ʾ��ת
    {MOT_MaxDuty,           9900},          //������ռ�ձ�(-10000~10000)
};
void Motor_Init(void)       //�����ʼ��
{
    //��ʼ�������������
    gpio_init(A8,GPO,GPIO_HIGH,GPO_PUSH_PULL); 
    gpio_init(A9,GPO,GPIO_HIGH,GPO_PUSH_PULL); 
	
    gpio_init(B24,GPO,GPIO_HIGH,GPO_PUSH_PULL); 
    gpio_init(B25,GPO,GPIO_HIGH,GPO_PUSH_PULL); 
    //��ʼ��PWM����
    pwm_init(PWM_TIM_A0_CH1_A7,MOT_CFG[MOT_Frequency][1],0); //B
    pwm_init(PWM_TIM_A0_CH3_B26,MOT_CFG[MOT_Frequency][1],0); 	//A 
}

void Motor_Control_Right(int32 dutyR)
{
    if(0<=dutyR) //��ת
    {
		if(dutyR>MOT_CFG[MOT_MaxDuty][1])
		{
			dutyR=MOT_CFG[MOT_MaxDuty][1];
		}
		gpio_set_level(B24,0);
		gpio_set_level(B25,1);
		pwm_set_duty(PWM_TIM_A0_CH3_B26, dutyR); 
    }
    else                //��ת
    {
		if(dutyR<MOT_CFG[MOT_MinDuty][1])
		{
			dutyR=MOT_CFG[MOT_MinDuty][1];
		}
		gpio_set_level(B24,1);
		gpio_set_level(B25,0);
		pwm_set_duty(PWM_TIM_A0_CH3_B26, -dutyR);
    }
}

void Motor_Control_Left(int32 dutyL)
{

    if(0<=dutyL) //��ת
    {  
	if(dutyL>MOT_CFG[MOT_MaxDuty][1])
	{
            dutyL=MOT_CFG[MOT_MaxDuty][1];
	}
		gpio_set_level(A8,1);
		gpio_set_level(A9,0);
		pwm_set_duty(PWM_TIM_A0_CH1_A7, dutyL);
    }
    else                //��ת
    {
	if(dutyL<MOT_CFG[MOT_MinDuty][1])
	{
            dutyL=MOT_CFG[MOT_MinDuty][1];
	}
		gpio_set_level(A8,0);
		gpio_set_level(A9,1);
		pwm_set_duty(PWM_TIM_A0_CH1_A7, -dutyL);
    }
}
void MotorPid_Right(float Vright)
{
    float Value_R=0;
    Value_R=PID_Increase(&Motor_R_speed,Vright,encoder.Encoder_R);//
	PWM_R+=Value_R;
	PWM_R+=Turn_Extern_data;
    Motor_Control_Right(PWM_R);
}
void MotorPid_Left(float Vleft)
{
    float Value_L=0;
    Value_L=PID_Increase(&Motor_L_speed,Vleft,encoder.Encoder_L);//
	PWM_L+=Value_L;
	PWM_L-=Turn_Extern_data;
    Motor_Control_Left(PWM_L);
}
void Turn_Extern_PID(float MidErr )
{
    Turn_Extern_data = PID_Realize(&Turn_PID,0,MidErr);
//    if(Turn_Extern_data>20)Turn_Extern_data=15;
//    if(Turn_Extern_data<-20)Turn_Extern_data=-15;
}
void Angle_PID(float Err )
{
    Turn_Extern_data = PID_Realize(&Ang_PID,Err,Angle_yaw);
//    if(Turn_Extern_data>15)Turn_Extern_data=15;
//    if(Turn_Extern_data<-15)Turn_Extern_data=-15;
}