#ifndef _search_h
#define _search_h

#include "zf_common_headfile.h"
#include "zf_device_ganwei_grayscale.h"

// 感为8路灰度传感器相关定义
#define GRAYSCALE_CHANNEL_NUM    8                    // 8路传感器通道数
#define GRAYSCALE_THRESHOLD      100                  // 黑白阈值
#define STRAIGHT_DETECT_THRESHOLD 70                  // 直角检测阈值

// 函数声明
void Gray_init(void);                                 // 灰度传感器初始化
void Get_gray(void);                                  // 获取灰度数据
void Gray_calibration(void);                          // 灰度传感器校准
float Calculate_line_error(void);                     // 计算循迹误差
uint8 Detect_straight_line(void);                     // 检测直线/直角

// 外部变量声明
extern ganwei_grayscale_info_struct grayscale_sensor; // 传感器结构体
extern uint16 gray_analog[GRAYSCALE_CHANNEL_NUM];     // 模拟量数据
extern uint16 gray_normalized[GRAYSCALE_CHANNEL_NUM]; // 归一化数据
extern uint8 gray_digital;                           // 数字量状态
extern float Gray_error;                              // 循迹误差
extern uint8 angle_flag;                              // 直角标志
extern uint8 sensor_init_flag;                       // 传感器初始化标志

#endif