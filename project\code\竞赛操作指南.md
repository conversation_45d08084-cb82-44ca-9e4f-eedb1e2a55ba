# 🏁 竞赛巡线操作指南

## 📋 题目要求
- **赛道**: 100cm×100cm正方形
- **线宽**: 1.8cm±0.2cm黑线
- **方向**: 逆时针行驶
- **圈数**: 1-5圈可设定
- **时间**: ≤20秒

## 🔧 硬件连接

### 感为8路灰度传感器
```
地址线0: A0
地址线1: A1  
地址线2: A2
ADC输入: A27
```

### 按键连接
```
KEY1 (功能选择): A10
KEY2 (参数+):   A11
KEY3 (参数-):   A12
KEY4 (启动停止): A13
```

### LED指示灯
```
LED1 (状态): B0
LED2 (圈数): B1
```

### 电机驱动 (TB6612)
```
左电机: A7(PWM), A8/A9(方向)
右电机: B26(PWM), B24/B25(方向)
```

## 🎮 按键操作

### 参数设置模式
1. **按KEY1** - 进入设置模式，LED1开始闪烁
2. **再按KEY1** - 切换参数类型（圈数→基础速度→转弯速度→灵敏度）
3. **按KEY2** - 增加当前参数
4. **按KEY3** - 减少当前参数
5. **按KEY4** - 退出设置，进入准备状态

### 运行控制
1. **准备状态** - LED1常亮，按KEY4开始
2. **运行状态** - LED1常亮，LED2闪烁，按KEY4停止
3. **完成状态** - 两LED交替闪烁，按KEY4复位

## ⚙️ 参数说明

### 1. 圈数设置 (1-5圈)
- **默认**: 3圈
- **调节**: KEY2增加，KEY3减少
- **显示**: LED2闪烁次数 = 圈数

### 2. 基础速度 (100-800)
- **默认**: 500
- **调节**: 每次±50
- **说明**: 直道行驶速度

### 3. 转弯速度 (50-400)
- **默认**: 250  
- **调节**: 每次±25
- **说明**: 检测到拐角时的速度

### 4. 转向灵敏度 (1-10)
- **默认**: 6
- **调节**: 每次±1
- **说明**: 数值越大转向越敏感

## 🚀 比赛流程

### 赛前准备
1. **上电检查** - 确认LED和传感器正常
2. **参数设置** - 根据赛道调整参数
3. **位置摆放** - 将小车放在起始位置
4. **最终确认** - 检查圈数设置

### 比赛执行
1. **按KEY4启动** - 小车开始巡线
2. **观察状态** - LED2闪烁表示正在运行
3. **自动停止** - 完成设定圈数或20秒超时
4. **查看结果** - LED交替闪烁表示完成

## 🎯 调试技巧

### 速度调优
- **起步慢**: 增加基础速度
- **转弯冲出**: 减少转弯速度
- **反应迟钝**: 增加灵敏度
- **摆动过大**: 减少灵敏度

### 常见问题
1. **不跟线**: 检查传感器连接和阈值
2. **转弯失控**: 降低转弯速度
3. **直道偏移**: 调整灵敏度
4. **圈数错误**: 检查起点检测逻辑

## 📊 推荐参数

### 保守型 (稳定优先)
```
圈数: 3
基础速度: 400
转弯速度: 200
灵敏度: 5
```

### 激进型 (速度优先)
```
圈数: 5
基础速度: 600
转弯速度: 300
灵敏度: 7
```

### 平衡型 (推荐)
```
圈数: 3
基础速度: 500
转弯速度: 250
灵敏度: 6
```

## ⚠️ 注意事项

1. **电池电量** - 确保电池充足，低电压影响性能
2. **传感器高度** - 保持传感器距离地面2-3mm
3. **环境光线** - 避免强光直射影响传感器
4. **赛道清洁** - 确保赛道无杂物和反光
5. **参数备份** - 记录最佳参数组合

## 🏆 比赛策略

### 时间分配
- **1圈**: 4-5秒
- **3圈**: 12-15秒  
- **5圈**: 18-20秒

### 速度策略
- **第1圈**: 稍慢，确保稳定
- **中间圈**: 正常速度
- **最后圈**: 可适当加速

### 应急处理
- **偏离赛道**: 立即按KEY4停止
- **传感器异常**: 重新上电初始化
- **电机异常**: 检查连接和驱动

## 📞 现场支持

比赛现场如遇问题：
1. 检查硬件连接
2. 重新设置参数
3. 重启系统初始化
4. 调整传感器位置

**祝您比赛顺利！🎉**
