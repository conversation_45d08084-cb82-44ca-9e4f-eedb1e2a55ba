# 🔌 8路灰度传感器接线图

## 📋 传感器引脚说明

您的8路灰度传感器有以下引脚：

```
┌─────────────────────────────────┐
│        8路灰度传感器              │
├─────────────────────────────────┤
│ GND  - 电源地                   │
│ +5V  - 电源正极(5V)             │
│ OUT  - 模拟输出(数据)           │
│ EN   - 使能信号                 │
│ AD2  - 地址线2(高位)            │
│ AD1  - 地址线1(中位)            │
│ AD0  - 地址线0(低位)            │
│ ERR  - 错误信号                 │
└─────────────────────────────────┘
```

## 🔗 正确接线方案

### 主要连接(必须)
```
传感器引脚    →    MSPM0G3507引脚    →    说明
─────────────────────────────────────────────────
GND          →    GND               →    电源地
+5V          →    5V电源             →    电源正极
OUT          →    A27               →    ADC数据输出
AD0          →    A0                →    地址线0(低位)
AD1          →    A1                →    地址线1(中位)
AD2          →    A2                →    地址线2(高位)
```

### 可选连接(建议)
```
传感器引脚    →    MSPM0G3507引脚    →    说明
─────────────────────────────────────────────────
EN           →    A26               →    使能控制
ERR          →    A3                →    错误检测
```

## 🎯 工作原理

### 地址线控制
8路传感器通过3根地址线选择当前读取的通道：

```
AD2  AD1  AD0  →  选择通道
 0    0    0   →  通道0
 0    0    1   →  通道1
 0    1    0   →  通道2
 0    1    1   →  通道3
 1    0    0   →  通道4
 1    0    1   →  通道5
 1    1    0   →  通道6
 1    1    1   →  通道7
```

### 数据读取流程
1. 设置地址线(AD2,AD1,AD0)选择通道
2. 等待稳定时间(通常几微秒)
3. 从OUT引脚读取ADC数值
4. 重复步骤1-3读取所有8个通道

## 🔧 硬件连接图

```
MSPM0G3507                    8路灰度传感器
┌─────────────┐              ┌─────────────┐
│             │              │             │
│ GND    ─────┼──────────────┼─── GND      │
│ 5V     ─────┼──────────────┼─── +5V      │
│ A27    ─────┼──────────────┼─── OUT      │
│ A0     ─────┼──────────────┼─── AD0      │
│ A1     ─────┼──────────────┼─── AD1      │
│ A2     ─────┼──────────────┼─── AD2      │
│ A26    ─────┼──────────────┼─── EN       │
│ A3     ─────┼──────────────┼─── ERR      │
│             │              │             │
└─────────────┘              └─────────────┘
```

## ⚡ 电源注意事项

### 电源要求
- **电压**: 5V (不要接3.3V)
- **电流**: 通常50-100mA
- **稳定性**: 需要稳定的5V电源

### 电源连接
```
方案1: 使用开发板5V输出
MSPM0G3507的5V引脚 → 传感器+5V

方案2: 外部5V电源
外部5V电源正极 → 传感器+5V
外部5V电源负极 → 传感器GND和MSPM0G3507 GND
```

## 🧪 测试验证

### 1. 电源测试
```c
// 上电后检查
debug_printf("传感器电源检查...\r\n");
```

### 2. 地址线测试
```c
// 测试地址线控制
gpio_set_level(A0, 0);  // AD0 = 0
gpio_set_level(A1, 0);  // AD1 = 0  
gpio_set_level(A2, 0);  // AD2 = 0
// 应该选择通道0
```

### 3. 数据读取测试
```c
// 读取ADC数值
uint16 adc_value = adc_get_data(ADC0_CH0_A27);
debug_printf("通道0数值: %d\r\n", adc_value);
```

## 🔍 故障排除

### 常见问题

#### 1. 读取数值异常
- **现象**: 数值始终为0或4095
- **原因**: 电源问题或OUT引脚未连接
- **解决**: 检查5V电源和OUT→A27连接

#### 2. 通道切换无效
- **现象**: 所有通道数值相同
- **原因**: 地址线未连接或连接错误
- **解决**: 检查AD0→A0, AD1→A1, AD2→A2连接

#### 3. 数值不稳定
- **现象**: 数值跳动很大
- **解决**: 
  - 检查电源稳定性
  - 增加延时等待
  - 检查接线是否牢固

#### 4. 使能信号问题
- **现象**: 传感器无响应
- **原因**: EN引脚未使能
- **解决**: 确保A26输出高电平使能传感器

## 📝 代码中的对应关系

```c
// 传感器初始化
ganwei_grayscale_init(&grayscale_sensor, GANWEI_GRAYSCALE_CLASS_EDITION,
                     GANWEI_GRAYSCALE_ADC_12BITS, A0, A1, A2, ADC0_CH0_A27);

// 使能信号控制
gpio_init(A26, GPO, GPIO_HIGH, GPO_PUSH_PULL);  // EN引脚
gpio_set_level(A26, 1);  // 使能传感器

// 数据获取
ganwei_grayscale_task(&grayscale_sensor);  // 更新数据
gray_data[i] = grayscale_sensor.analog_value[i];  // 获取通道i的数据
```

## 🧪 详细测试步骤

### 第一步：基础连接测试
1. **只连接电源**
   ```
   GND → GND
   +5V → 5V电源
   ```

2. **检查电源**
   - 用万用表测量传感器+5V引脚电压
   - 应该显示接近5.0V

### 第二步：使能信号测试
1. **连接使能引脚**
   ```
   EN → A26
   ```

2. **代码测试**
   ```c
   gpio_init(A26, GPO, GPIO_HIGH, GPO_PUSH_PULL);
   gpio_set_level(A26, 1);  // 使能
   debug_printf("传感器已使能\r\n");
   ```

### 第三步：地址线测试
1. **连接地址线**
   ```
   AD0 → A0
   AD1 → A1
   AD2 → A2
   ```

2. **测试地址切换**
   ```c
   // 选择通道0
   gpio_set_level(A0, 0);  // AD0 = 0
   gpio_set_level(A1, 0);  // AD1 = 0
   gpio_set_level(A2, 0);  // AD2 = 0

   // 选择通道7
   gpio_set_level(A0, 1);  // AD0 = 1
   gpio_set_level(A1, 1);  // AD1 = 1
   gpio_set_level(A2, 1);  // AD2 = 1
   ```

### 第四步：数据读取测试
1. **连接数据线**
   ```
   OUT → A27
   ```

2. **读取测试**
   ```c
   ganwei_grayscale_task(&grayscale_sensor);
   for(uint8 i = 0; i < 8; i++)
   {
       debug_printf("通道%d: %d\r\n", i, grayscale_sensor.analog_value[i]);
   }
   ```

### 第五步：功能验证
1. **白纸测试**
   - 将传感器放在白纸上
   - 所有通道数值应该较大(>2000)

2. **黑线测试**
   - 将传感器放在黑线上
   - 对应通道数值应该较小(<500)

3. **位置测试**
   - 移动传感器位置
   - 观察不同通道数值变化

## ✅ 接线检查清单

- [ ] GND → GND (电源地)
- [ ] +5V → 5V电源 (电源正极)
- [ ] OUT → A27 (数据输出)
- [ ] AD0 → A0 (地址线0)
- [ ] AD1 → A1 (地址线1)
- [ ] AD2 → A2 (地址线2)
- [ ] EN → A26 (使能信号，可选)
- [ ] ERR → A3 (错误检测，可选)

## 🎯 下一步

接线完成后：
1. 编译并下载程序
2. 观察串口输出
3. 手动测试传感器响应
4. 调整参数优化效果

**接线完成后就可以测试了！🚀**
