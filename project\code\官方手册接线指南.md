# 📖 8路灰度传感器官方接线指南

## 📋 根据官方手册的正确接线

### 传感器引脚功能(官方定义)
```
GND  - 电源负极
+5V  - 电源正极(5V供电)
OUT  - 模拟输出信号(连接74HC4051的OUT)
EN   - 使能端口(低电平使能，高电平禁止，可悬空默认使能)
AD0  - 地址0(选择输出通道)
AD1  - 地址1(选择输出通道)  
AD2  - 地址2(选择输出通道)
ERR  - 错误端口(与错误指示灯相连，可悬空)
```

### 🔗 正确接线方案

#### 必须连接
```
传感器引脚    →    MSPM0G3507引脚    →    说明
─────────────────────────────────────────────────
GND          →    GND               →    电源地
+5V          →    独立5V电源         →    ⚠️不要与电机共用电源!
OUT          →    A27               →    模拟输出(ADC输入)
AD0          →    A0                →    地址线0
AD1          →    A1                →    地址线1
AD2          →    A2                →    地址线2
```

#### 可选连接
```
EN           →    A26 或 悬空        →    低电平使能(悬空默认使能)
ERR          →    A3 或 悬空         →    错误检测(可选)
```

## ⚡ 重要电源要求

### ❌ 错误做法
```
❌ 与电机、舵机共用5V电源
❌ 使用3.3V供电
❌ 电源驱动能力不足(<150mA)
```

### ✅ 正确做法
```
✅ 独立稳定的5V电源
✅ 电源驱动能力>150mA
✅ 与感性负载(电机/舵机)分离供电
```

### 推荐电源方案
```
方案1: 独立5V电源模块
外部5V电源 → 传感器+5V
外部GND → 传感器GND和MCU GND

方案2: 开发板5V输出(如果功率足够)
开发板5V → 传感器+5V
开发板GND → 传感器GND
```

## 🎯 通道选择真值表(官方)

```
EN  AD2  AD1  AD0  →  输出通道
0    0    0    0   →  第1路
0    0    0    1   →  第2路
0    0    1    0   →  第3路
0    0    1    1   →  第4路
0    1    0    0   →  第5路
0    1    0    1   →  第6路
0    1    1    0   →  第7路
0    1    1    1   →  第8路
1    ×    ×    ×   →  断电(禁止)
```

### 关键说明
- **EN=0**: 使能传感器(正常工作)
- **EN=1**: 断电(整个传感器断电)
- **EN悬空**: 默认使能(内部10K下拉电阻)

## 🔧 代码配置

### 传感器初始化
```c
// 8路灰度传感器初始化
ganwei_grayscale_init(&grayscale_sensor, GANWEI_GRAYSCALE_CLASS_EDITION, 
                     GANWEI_GRAYSCALE_ADC_12BITS, A0, A1, A2, ADC0_CH0_A27);

// 使能信号 - 低电平使能
gpio_init(A26, GPO, GPIO_LOW, GPO_PUSH_PULL);
gpio_set_level(A26, 0);  // EN=0使能传感器
```

### 数据获取
```c
// 更新传感器数据
ganwei_grayscale_task(&grayscale_sensor);

// 获取8路数据
for(uint8 i = 0; i < 8; i++)
{
    gray_data[i] = grayscale_sensor.analog_value[i];
}
```

## 🧪 官方试机步骤

### 第一步：电源测试
1. **只连接电源**
   ```
   +5V → 独立5V电源正极
   GND → 电源负极
   ```
2. **检查指示灯**
   - 绿色电源指示灯应该亮起
   - 如果不亮，检查EN是否接了高电平

### 第二步：探头测试
1. **观察探头发光**
   - 可见光版本：肉眼可见
   - 红外版本：用手机摄像头观察
2. **如果不亮**
   - 检查EN引脚是否悬空或接低电平
   - 检查电源电压是否真的是5V

### 第三步：信号测试
1. **万用表测试**
   ```
   黑表笔 → GND
   红表笔 → OUT引脚
   ```
2. **放置白纸**
   - 传感器下方放白纸
   - 万用表应显示较高电压(接近5V)
3. **遮挡测试**
   - 用手遮挡传感器
   - 万用表电压应明显下降

### 第四步：通道切换测试
1. **连接地址线**
   ```
   AD0 → A0, AD1 → A1, AD2 → A2
   ```
2. **代码测试**
   ```c
   // 选择不同通道，观察数据变化
   ganwei_grayscale_task(&grayscale_sensor);
   debug_printf("通道数据: ");
   for(uint8 i = 0; i < 8; i++)
   {
       debug_printf("%d ", grayscale_sensor.analog_value[i]);
   }
   ```

## ⚠️ 常见问题解决

### 问题1：电源指示灯不亮
**可能原因：**
- EN引脚接了高电平
- 电源电压不足或接反
- 焊接虚焊

**解决方法：**
- 确保EN悬空或接低电平
- 用万用表测量实际电源电压
- 检查焊接质量

### 问题2：探头不发光
**可能原因：**
- EN引脚问题
- 电源问题
- 红外版本用肉眼看不见

**解决方法：**
- 检查EN引脚状态
- 红外版本用手机摄像头观察
- 检查电源稳定性

### 问题3：输出信号异常
**可能原因：**
- OUT引脚连接问题
- ADC配置错误
- 电源不稳定

**解决方法：**
- 确保OUT→A27连接正确
- 检查ADC引脚配置
- 使用独立稳定电源

### 问题4：8个通道数据不一致
**这是正常现象！**
- 每个探头灵敏度天生不同
- 电容电阻值有波动
- 可通过软件校准解决

## 📊 预期数据范围

### 正常工作状态
```
白纸/白色表面: 3000-4095 (接近满量程)
灰色表面:     1500-3000
黑线/黑色表面: 0-800
```

### 异常状态识别
```
始终为0:     可能断电或EN被禁止
始终为4095:  可能OUT引脚未连接
数据跳动大:   电源不稳定
```

## ✅ 接线检查清单

- [ ] **电源独立** - 5V电源与电机分离
- [ ] **电压正确** - 用万用表确认真的是5V
- [ ] **GND连接** - 传感器GND与MCU GND相连
- [ ] **OUT连接** - OUT→A27(ADC引脚)
- [ ] **地址线** - AD0→A0, AD1→A1, AD2→A2
- [ ] **使能正确** - EN悬空或接低电平
- [ ] **焊接牢固** - 所有焊点无虚焊
- [ ] **指示灯亮** - 绿色电源灯正常亮起

## 🎯 下一步测试

接线完成后：
1. **上电检查** - 观察指示灯状态
2. **探头检查** - 确认探头发光
3. **信号测试** - 万用表测试OUT信号
4. **代码测试** - 运行程序观察数据
5. **功能验证** - 白纸黑线测试效果

**严格按照官方手册操作，确保成功！🚀**
