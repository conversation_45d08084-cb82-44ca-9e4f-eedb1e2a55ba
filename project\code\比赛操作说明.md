# 🏁 竞赛巡线操作说明

## 📋 题目要求
- **赛道**: 100cm×100cm正方形，黑线宽1.8cm±0.2cm
- **方向**: 逆时针行驶
- **圈数**: 1-5圈可设定
- **时间**: ≤20秒

## 🔌 硬件连接

### 感为8路灰度传感器
```
地址线0: A0
地址线1: A1  
地址线2: A2
ADC输入: A27
```

### 按键
```
KEY1: A10 (参数选择)
KEY2: A11 (参数+)
KEY3: A12 (参数-)
KEY4: A13 (启动/停止)
```

### LED指示
```
LED1: B0 (系统状态)
LED2: B1 (圈数指示)
```

### 电机 (TB6612)
```
左电机PWM: A7
左电机方向: A8/A9
右电机PWM: B26
右电机方向: B24/B25
```

## 🎮 操作流程

### 1. 上电初始化
- 系统启动后LED全灭
- 串口输出初始化信息

### 2. 参数设置
1. **按KEY1** → 进入设置模式 (LED1闪烁)
2. **再按KEY1** → 切换参数类型
   - 模式0: 圈数设置 (1-5)
   - 模式1: 直道速度 (100-800)
   - 模式2: 转弯速度 (50-400)  
   - 模式3: 转向灵敏度 (1-10)
3. **按KEY2** → 增加当前参数
4. **按KEY3** → 减少当前参数
5. **按KEY4** → 退出设置，进入准备状态

### 3. 开始比赛
1. **准备状态** → LED1常亮，将小车放在起始位置
2. **按KEY4** → 开始巡线 (LED1常亮，LED2闪烁)
3. **自动完成** → 达到设定圈数或20秒后自动停止
4. **完成状态** → 两LED交替闪烁

### 4. 紧急停止
- **运行中按KEY4** → 立即停止

## ⚙️ 默认参数
```
目标圈数: 3圈
直道速度: 500
转弯速度: 250
转向灵敏度: 6
```

## 🎯 调试建议

### 速度调优
- **起步慢** → 增加直道速度
- **转弯冲出** → 减少转弯速度  
- **反应迟钝** → 增加灵敏度
- **摆动过大** → 减少灵敏度

### LED状态说明
- **全灭**: 停止状态
- **LED1闪烁**: 设置模式
- **LED1常亮**: 准备状态
- **LED1常亮+LED2闪烁**: 运行状态
- **两LED交替闪烁**: 完成状态

### 串口调试信息
- 参数变更时输出当前参数
- 完成圈数时输出提示
- 比赛结束时输出结果

## ⚠️ 注意事项

1. **传感器高度**: 保持距离地面2-3mm
2. **电池电量**: 确保电池充足
3. **环境光线**: 避免强光直射
4. **赛道清洁**: 确保无杂物
5. **参数记录**: 记录最佳参数组合

## 🏆 比赛策略

### 推荐参数组合

**稳定型** (适合新手):
```
圈数: 3
直道速度: 400
转弯速度: 200
灵敏度: 5
```

**平衡型** (推荐):
```
圈数: 3  
直道速度: 500
转弯速度: 250
灵敏度: 6
```

**激进型** (追求速度):
```
圈数: 5
直道速度: 600
转弯速度: 300
灵敏度: 7
```

## 🔧 故障排除

### 常见问题
1. **不跟线** → 检查传感器连接和高度
2. **转弯失控** → 降低转弯速度和灵敏度
3. **直道偏移** → 调整灵敏度参数
4. **圈数错误** → 检查起点标记是否明显

### 应急处理
- **偏离赛道** → 立即按KEY4停止
- **参数错误** → 重新进入设置模式调整
- **系统异常** → 重新上电初始化

**祝您比赛成功！🎉**
