# 🧪 测试版本说明

## 📋 当前版本特点
- **上电直接运行** - 无需按键操作
- **掉电自动停止** - 断电即停
- **实时调试输出** - 串口显示传感器数据
- **LED状态指示** - 直观显示运行状态

## 🔌 硬件连接

### 必须连接
```
感为8路灰度传感器:
- 地址线0: A0
- 地址线1: A1  
- 地址线2: A2
- ADC输入: A27

电机驱动(TB6612):
- 左电机PWM: A7
- 左电机方向: A8/A9
- 右电机PWM: B26
- 右电机方向: B24/B25

LED指示(可选):
- LED1: B0 (运行状态)
- LED2: B1 (闪烁指示)
```

### 暂时不用连接
```
按键: A10/A11/A12/A13 (已屏蔽)
```

## ⚙️ 当前参数设置

```c
目标圈数: 3圈
直道速度: 500
转弯速度: 250
转向灵敏度: 6
黑线检测阈值: 100
```

## 🚀 测试流程

### 1. 上电测试
1. **连接串口** - 波特率115200
2. **上电启动** - 系统自动初始化
3. **观察输出** - 串口显示初始化信息
4. **LED指示** - LED1常亮，LED2闪烁表示运行

### 2. 传感器测试
1. **放在白纸上** - 观察传感器数值(应该较大，>500)
2. **放在黑线上** - 观察传感器数值(应该较小，<100)
3. **移动位置** - 观察线位置变化(0-7)

### 3. 电机测试
1. **抬起小车** - 观察电机是否转动
2. **检查方向** - 左右电机转向是否正确
3. **速度观察** - 直道和转弯速度差异

## 📊 串口调试信息

### 启动信息
```
=== 竞赛巡线系统启动 ===
硬件初始化完成
上电直接运行模式 - 目标3圈
参数: 直道500 转弯250 灵敏度6
开始巡线...
```

### 运行信息 (每500ms输出)
```
传感器: 1023 1023 1023 50 30 1023 1023 1023 | 位置:3 | 检测:1 | 拐角:0
传感器: 1023 1023 20 15 10 25 1023 1023 | 位置:3 | 检测:1 | 拐角:1
```

### 圈数信息
```
完成第1圈
完成第2圈
完成第3圈
比赛完成！
```

## 🔧 参数调整

如需调整参数，直接修改main.c中的数值：

```c
// ========================= 系统参数 =========================
uint8 target_laps = 3;          // 目标圈数(1-5)
uint16 base_speed = 500;        // 直道速度(100-800)
uint16 turn_speed = 250;        // 转弯速度(50-400)
uint8 sensitivity = 6;          // 转向灵敏度(1-10)
```

### 常用调整
- **速度太慢** → 增加base_speed和turn_speed
- **转弯冲出** → 减少turn_speed
- **反应迟钝** → 增加sensitivity
- **摆动过大** → 减少sensitivity
- **检测不准** → 调整阈值100

## 🎯 测试重点

### 1. 传感器检测
- [ ] 白纸上数值 > 500
- [ ] 黑线上数值 < 100  
- [ ] 线位置计算正确(0-7)
- [ ] 拐角检测正常

### 2. 电机控制
- [ ] 左电机正转/反转正常
- [ ] 右电机正转/反转正常
- [ ] 差速控制有效
- [ ] 速度切换正常

### 3. 巡线效果
- [ ] 能跟随直线
- [ ] 能通过转弯
- [ ] 圈数计数准确
- [ ] 整体稳定性

## ⚠️ 注意事项

1. **安全第一** - 测试时注意小车不要掉落
2. **传感器高度** - 保持距离地面2-3mm
3. **电池电量** - 确保电池充足
4. **串口监控** - 实时观察调试信息
5. **参数记录** - 记录有效的参数组合

## 🔄 下一步计划

测试完成后可以：
1. **恢复按键功能** - 取消注释按键代码
2. **添加超时保护** - 恢复20秒限制
3. **优化参数** - 根据测试结果调整
4. **完善功能** - 添加更多控制逻辑

## 📞 问题反馈

如遇问题请检查：
1. 硬件连接是否正确
2. 传感器数值是否正常
3. 电机转向是否正确
4. 参数设置是否合理

**测试愉快！🎉**
