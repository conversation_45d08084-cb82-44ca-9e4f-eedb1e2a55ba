#include "search.h"
float weight[5]={-4,-2,0,2,4};
float Gray_error;
uint8 hongwai[5]={0,0,0,0,0};
uint16 huidu[5]={0,0,0,0,0};
float huiduweight[5]={-1.2,-1,0,1,1.2};
void Gray_init(void)
{
	
//	gpio_init(A27, GPI, GPIO_HIGH, GPI_PULL_UP);
//	gpio_init(A25, GPI, GPIO_HIGH, GPI_PULL_UP);
//	gpio_init(A22, GPI, GPIO_HIGH, GPI_PULL_UP);
//	gpio_init(A16, GPI, GPIO_HIGH, GPI_PULL_UP);
//	gpio_init(A15, GPI, GPIO_HIGH, GPI_PULL_UP);
	
	adc_init(ADC0_CH0_A27,ADC_8BIT);
	adc_init(ADC0_CH2_A25,ADC_8BIT);
	adc_init(ADC0_CH7_A22,ADC_8BIT);
	adc_init(ADC1_CH1_A16,ADC_8BIT);
	adc_init(ADC1_CH0_A15,ADC_8BIT);
	
}


void Get_gray(void)
{
//	if(gpio_get_level(A27)==0)hongwai[4]=1;
//	else hongwai[4]=0;
//	if(gpio_get_level(A25)==0)hongwai[3]=1;
//	else hongwai[3]=0;
//	if(gpio_get_level(A22)==0)hongwai[2]=1;
//	else hongwai[2]=0;
//	if(gpio_get_level(A16)==0)hongwai[1]=1;
//	else hongwai[1]=0;
//	if(gpio_get_level(A15)==0)hongwai[0]=1;
//	else hongwai[0]=0;
//	if(hongwai[0]==0&&hongwai[1]==0&&hongwai[2]==0&&hongwai[3]==0&&hongwai[4]==0)
//	{
//		angle_flag=1;
//		Angle_yaw=0;
//	}
//	else //if(hongwai[2]==0)
//	{
//		angle_flag=0;
//	}
//	Gray_error=hongwai[0]*weight[0]+hongwai[1]*weight[1]+hongwai[2]*weight[2]+hongwai[3]*weight[3]+hongwai[4]*weight[4];
	
	huidu[0]=adc_convert(ADC1_CH0_A15);
	huidu[1]=adc_convert(ADC1_CH1_A16);
	huidu[2]=adc_convert(ADC0_CH7_A22);
	huidu[3]=adc_convert(ADC0_CH2_A25);
	huidu[4]=adc_convert(ADC0_CH0_A27);
	if(huidu[0]<70&&huidu[1]<70&&huidu[2]<70&&huidu[3]<70&&huidu[4]<70)angle_flag=1;
//	else angle_flag=0;
//	if(angle_flag==0)
		Gray_error=huidu[0]*huiduweight[0]+huidu[1]*huiduweight[1]+huidu[2]*huiduweight[2]+huidu[3]*huiduweight[3]+huidu[4]*huiduweight[4];
	
}
uint8 angle_flag=0;
//void zhijiao(void)
//{
//		angle_flag=0;
//}