#include "search.h"

// 感为8路灰度传感器实例
ganwei_grayscale_info_struct grayscale_sensor;

// 数据存储数组
uint16 gray_analog[GRAYSCALE_CHANNEL_NUM] = {0};      // 模拟量数据
uint16 gray_normalized[GRAYSCALE_CHANNEL_NUM] = {0};  // 归一化数据
uint8 gray_digital = 0;                               // 数字量状态

// 循迹相关变量
float Gray_error = 0.0f;                              // 循迹误差
uint8 angle_flag = 0;                                 // 直角检测标志
uint8 sensor_init_flag = 0;                          // 传感器初始化标志

// 8路传感器权重系数（中心对称分布）
float weight[GRAYSCALE_CHANNEL_NUM] = {-3.5f, -2.5f, -1.5f, -0.5f, 0.5f, 1.5f, 2.5f, 3.5f};

// 校准数据存储
uint16 white_calibration[GRAYSCALE_CHANNEL_NUM] = {0}; // 白校准值
uint16 black_calibration[GRAYSCALE_CHANNEL_NUM] = {0}; // 黑校准值

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     感为8路灰度传感器初始化
// 参数说明     void
// 返回参数     void
// 使用示例     Gray_init();
// 备注信息     初始化感为8路灰度传感器，配置地址线和ADC
//-------------------------------------------------------------------------------------------------------------------
void Gray_init(void)
{
    // 初始化感为8路灰度传感器
    // 地址线: A0(addr0), A1(addr1), A2(addr2)
    // ADC通道: ADC0_CH0_A27
    uint8 init_result = ganwei_grayscale_init(&grayscale_sensor,
                                              GANWEI_GRAYSCALE_CLASS_EDITION,  // 经典版
                                              GANWEI_GRAYSCALE_ADC_12BITS,     // 12位ADC
                                              A0,                              // 地址线0
                                              A1,                              // 地址线1
                                              A2,                              // 地址线2
                                              ADC0_CH0_A27);                   // ADC通道

    if(init_result)
    {
        sensor_init_flag = 1;
        // 设置传感器方向（根据实际安装方向调整）
        ganwei_grayscale_set_direction(&grayscale_sensor, 0); // 0-正向，1-反向
    }
    else
    {
        sensor_init_flag = 0;
        // 初始化失败处理
        debug_printf("Grayscale sensor init failed!\r\n");
    }
}


//-------------------------------------------------------------------------------------------------------------------
// 函数简介     获取8路灰度传感器数据
// 参数说明     void
// 返回参数     void
// 使用示例     Get_gray();
// 备注信息     获取传感器数据并计算循迹误差，建议在主循环中调用
//-------------------------------------------------------------------------------------------------------------------
void Get_gray(void)
{
    if(!sensor_init_flag) return; // 传感器未初始化则返回

    // 执行传感器任务（数据采集和处理）
    ganwei_grayscale_task(&grayscale_sensor);

    // 获取模拟量数据
    ganwei_grayscale_get_analog(&grayscale_sensor, gray_analog);

    // 获取数字量状态
    gray_digital = ganwei_grayscale_get_digital(&grayscale_sensor);

    // 如果已完成校准，获取归一化数据
    if(grayscale_sensor.init_flag)
    {
        ganwei_grayscale_get_normalized(&grayscale_sensor, gray_normalized);
    }

    // 检测直角/直线
    angle_flag = Detect_straight_line();

    // 计算循迹误差
    Gray_error = Calculate_line_error();
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     灰度传感器校准
// 参数说明     void
// 返回参数     void
// 使用示例     Gray_calibration();
// 备注信息     在白纸和黑线上分别调用进行校准
//-------------------------------------------------------------------------------------------------------------------
void Gray_calibration(void)
{
    uint8 i;

    // 采集当前环境下的数据作为校准基准
    ganwei_grayscale_get_analog(&grayscale_sensor, gray_analog);

    // 这里可以根据实际需要实现白校准和黑校准
    // 示例：假设当前为白纸环境
    for(i = 0; i < GRAYSCALE_CHANNEL_NUM; i++)
    {
        white_calibration[i] = gray_analog[i];
        black_calibration[i] = gray_analog[i] / 2; // 简单估算黑值
    }

    // 使用校准数据初始化传感器
    ganwei_grayscale_init_with_calibration(&grayscale_sensor, white_calibration, black_calibration);
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     计算循迹误差
// 参数说明     void
// 返回参数     float           循迹误差值
// 使用示例     float error = Calculate_line_error();
// 备注信息     基于8路传感器数据计算加权误差
//-------------------------------------------------------------------------------------------------------------------
float Calculate_line_error(void)
{
    uint8 i;
    float error = 0.0f;
    float total_weight = 0.0f;
    uint16 *data_source;

    // 选择数据源（优先使用归一化数据）
    if(grayscale_sensor.init_flag)
    {
        data_source = gray_normalized;
    }
    else
    {
        data_source = gray_analog;
    }

    // 计算加权误差
    for(i = 0; i < GRAYSCALE_CHANNEL_NUM; i++)
    {
        error += (float)data_source[i] * weight[i];
        total_weight += (float)data_source[i];
    }

    // 归一化处理（避免除零）
    if(total_weight > 0.1f)
    {
        error = error / total_weight;
    }
    else
    {
        error = 0.0f; // 无有效信号时误差为0
    }

    return error;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     检测直线/直角
// 参数说明     void
// 返回参数     uint8           1-检测到直角/直线，0-正常循迹
// 使用示例     uint8 straight = Detect_straight_line();
// 备注信息     基于8路传感器数据判断是否为直角或直线
//-------------------------------------------------------------------------------------------------------------------
uint8 Detect_straight_line(void)
{
    uint8 i;
    uint8 black_count = 0;
    uint16 *data_source;

    // 选择数据源
    if(grayscale_sensor.init_flag)
    {
        data_source = gray_normalized;
    }
    else
    {
        data_source = gray_analog;
    }

    // 统计低于阈值的传感器数量
    for(i = 0; i < GRAYSCALE_CHANNEL_NUM; i++)
    {
        if(data_source[i] < STRAIGHT_DETECT_THRESHOLD)
        {
            black_count++;
        }
    }

    // 如果大部分传感器都检测到黑线，认为是直角或直线
    if(black_count >= 6) // 8路中有6路以上检测到黑线
    {
        return 1;
    }

    return 0;
}