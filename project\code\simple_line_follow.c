#include "simple_line_follow.h"

// 全局变量
line_follow_params_t line_params = {
    .target_laps = 1,       // 默认1圈
    .base_speed = 400,      // 默认基础速度
    .turn_speed = 200,      // 默认转弯速度
    .sensitivity = 5,       // 默认灵敏度
    .current_laps = 0,
    .state = STATE_STOP,
    .param_mode = PARAM_LAP_COUNT
};

line_control_t line_control = {0};

// 按键状态 (外部定义)
extern uint8 key_state[4];  // 按键当前状态
extern uint8 key_last[4];   // 按键上次状态

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     简单巡线系统初始化
// 参数说明     void
// 返回参数     void
// 使用示例     Simple_Line_Follow_Init();
// 备注信息     初始化按键、LED、传感器、电机
//-------------------------------------------------------------------------------------------------------------------
void Simple_Line_Follow_Init(void)
{
    // 初始化按键
    gpio_init(KEY1_PIN, GPI, GPIO_HIGH, GPI_PULL_UP);
    gpio_init(KEY2_PIN, GPI, GPIO_HIGH, GPI_PULL_UP);
    gpio_init(KEY3_PIN, GPI, GPIO_HIGH, GPI_PULL_UP);
    gpio_init(KEY4_PIN, GPI, GPIO_HIGH, GPI_PULL_UP);
    
    // 初始化LED
    gpio_init(LED1_PIN, GPO, GPIO_LOW, GPO_PUSH_PULL);
    gpio_init(LED2_PIN, GPO, GPIO_LOW, GPO_PUSH_PULL);
    
    // 初始化传感器和电机
    Gray_init();
    Motor_Init();
    
    // 显示初始参数
    Display_Parameters();
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     按键扫描
// 参数说明     void
// 返回参数     void
// 使用示例     Key_Scan();
// 备注信息     扫描4个按键，检测按下事件
//-------------------------------------------------------------------------------------------------------------------
void Key_Scan(void)
{
    // 读取按键状态
    key_state[0] = gpio_get_level(KEY1_PIN);
    key_state[1] = gpio_get_level(KEY2_PIN);
    key_state[2] = gpio_get_level(KEY3_PIN);
    key_state[3] = gpio_get_level(KEY4_PIN);
    
    // KEY1: 功能选择键
    if(key_last[0] == 1 && key_state[0] == 0)
    {
        if(line_params.state == STATE_SETTING)
        {
            line_params.param_mode = (line_params.param_mode + 1) % PARAM_MAX;
            Display_Parameters();
        }
        else if(line_params.state == STATE_STOP)
        {
            line_params.state = STATE_SETTING;
            Display_Parameters();
        }
    }
    
    // KEY2: 参数+键
    if(key_last[1] == 1 && key_state[1] == 0)
    {
        if(line_params.state == STATE_SETTING)
        {
            Parameter_Setting();
            Display_Parameters();
        }
    }
    
    // KEY3: 参数-键
    if(key_last[2] == 1 && key_state[2] == 0)
    {
        if(line_params.state == STATE_SETTING)
        {
            // 参数减少逻辑（与KEY2相反）
            switch(line_params.param_mode)
            {
                case PARAM_LAP_COUNT:
                    if(line_params.target_laps > 1) line_params.target_laps--;
                    break;
                case PARAM_BASE_SPEED:
                    if(line_params.base_speed > 100) line_params.base_speed -= 50;
                    break;
                case PARAM_TURN_SPEED:
                    if(line_params.turn_speed > 50) line_params.turn_speed -= 25;
                    break;
                case PARAM_SENSITIVITY:
                    if(line_params.sensitivity > 1) line_params.sensitivity--;
                    break;
                case PARAM_MAX:
                default:
                    // 无需处理
                    break;
            }
            Display_Parameters();
        }
    }
    
    // KEY4: 启动/停止键
    if(key_last[3] == 1 && key_state[3] == 0)
    {
        if(line_params.state == STATE_SETTING)
        {
            line_params.state = STATE_READY;
            line_params.current_laps = 0;
        }
        else if(line_params.state == STATE_READY)
        {
            line_params.state = STATE_RUNNING;
        }
        else if(line_params.state == STATE_RUNNING)
        {
            line_params.state = STATE_STOP;
            Motor_Control_Left(0);
            Motor_Control_Right(0);
        }
        else if(line_params.state == STATE_FINISHED)
        {
            line_params.state = STATE_STOP;
            line_params.current_laps = 0;
        }
    }
    
    // 更新按键历史状态
    for(uint8 i = 0; i < 4; i++)
    {
        key_last[i] = key_state[i];
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     参数设置
// 参数说明     void
// 返回参数     void
// 使用示例     Parameter_Setting();
// 备注信息     根据当前参数模式调整参数
//-------------------------------------------------------------------------------------------------------------------
void Parameter_Setting(void)
{
    switch(line_params.param_mode)
    {
        case PARAM_LAP_COUNT:
            if(line_params.target_laps < 5) line_params.target_laps++;
            break;
        case PARAM_BASE_SPEED:
            if(line_params.base_speed < 800) line_params.base_speed += 50;
            break;
        case PARAM_TURN_SPEED:
            if(line_params.turn_speed < 400) line_params.turn_speed += 25;
            break;
        case PARAM_SENSITIVITY:
            if(line_params.sensitivity < 10) line_params.sensitivity++;
            break;
        case PARAM_MAX:
        default:
            // 无需处理
            break;
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     LED显示
// 参数说明     void
// 返回参数     void
// 使用示例     LED_Display();
// 备注信息     根据系统状态控制LED显示
//-------------------------------------------------------------------------------------------------------------------
void LED_Display(void)
{
    static uint16 led_counter = 0;
    led_counter++;
    
    switch(line_params.state)
    {
        case STATE_STOP:
            gpio_set_level(LED1_PIN, 0);  // LED1关闭
            gpio_set_level(LED2_PIN, 0);  // LED2关闭
            break;
        case STATE_SETTING:
            // LED1闪烁表示设置模式
            gpio_set_level(LED1_PIN, (led_counter / 100) % 2);
            gpio_set_level(LED2_PIN, 0);
            break;
        case STATE_READY:
            gpio_set_level(LED1_PIN, 1);  // LED1常亮表示准备
            gpio_set_level(LED2_PIN, 0);
            break;
        case STATE_RUNNING:
            gpio_set_level(LED1_PIN, 1);  // LED1常亮
            // LED2根据圈数闪烁
            gpio_set_level(LED2_PIN, (led_counter / 200) % 2);
            break;
        case STATE_FINISHED:
            // 两个LED交替闪烁表示完成
            gpio_set_level(LED1_PIN, (led_counter / 150) % 2);
            gpio_set_level(LED2_PIN, !((led_counter / 150) % 2));
            break;
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     简单线检测
// 参数说明     void
// 返回参数     void
// 使用示例     Simple_Line_Detection();
// 备注信息     基于8路传感器的简单线位置检测
//-------------------------------------------------------------------------------------------------------------------
void Simple_Line_Detection(void)
{
    Get_gray();  // 获取传感器数据
    
    // 简单的线位置检测算法
    uint8 sensor_count = 0;
    uint16 position_sum = 0;
    
    // 统计检测到线的传感器
    for(uint8 i = 0; i < GRAYSCALE_CHANNEL_NUM; i++)
    {
        if(gray_analog[i] < 100)  // 检测到黑线
        {
            sensor_count++;
            position_sum += i;
        }
    }
    
    if(sensor_count > 0)
    {
        line_control.line_detected = 1;
        line_control.line_position = position_sum / sensor_count;  // 平均位置
        
        // 检测拐角（多个传感器同时检测到）
        if(sensor_count >= 4)
        {
            line_control.corner_detected = 1;
        }
        else
        {
            line_control.corner_detected = 0;
        }
    }
    else
    {
        line_control.line_detected = 0;
        line_control.corner_detected = 0;
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     简单电机控制
// 参数说明     void
// 返回参数     void
// 使用示例     Simple_Motor_Control();
// 备注信息     基于线位置的简单差速控制
//-------------------------------------------------------------------------------------------------------------------
void Simple_Motor_Control(void)
{
    if(line_params.state != STATE_RUNNING)
    {
        Motor_Control_Left(0);
        Motor_Control_Right(0);
        return;
    }
    
    if(!line_control.line_detected)
    {
        // 没检测到线，停止
        Motor_Control_Left(0);
        Motor_Control_Right(0);
        return;
    }
    
    // 计算转向偏差 (3.5为中心位置)
    float error = (float)line_control.line_position - 3.5f;
    
    // 基础速度
    int16 base_speed = line_control.corner_detected ? line_params.turn_speed : line_params.base_speed;
    
    // 简单的差速控制
    int16 turn_value = (int16)(error * line_params.sensitivity * 10);
    
    line_control.motor_left = base_speed - turn_value;
    line_control.motor_right = base_speed + turn_value;
    
    // 限幅
    if(line_control.motor_left > 800) line_control.motor_left = 800;
    if(line_control.motor_left < -800) line_control.motor_left = -800;
    if(line_control.motor_right > 800) line_control.motor_right = 800;
    if(line_control.motor_right < -800) line_control.motor_right = -800;
    
    // 输出到电机
    Motor_Control_Left(line_control.motor_left);
    Motor_Control_Right(line_control.motor_right);
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     圈数计数
// 参数说明     void
// 返回参数     void
// 使用示例     Lap_Counter();
// 备注信息     简单的圈数检测逻辑
//-------------------------------------------------------------------------------------------------------------------
void Lap_Counter(void)
{
    static uint8 start_line_detected = 0;
    static uint16 no_start_counter = 0;
    
    // 检测起始线（假设起始位置有特殊标记或全黑）
    if(line_control.corner_detected && !start_line_detected)
    {
        start_line_detected = 1;
        line_params.current_laps++;
        no_start_counter = 0;
        
        // 检查是否完成目标圈数
        if(line_params.current_laps >= line_params.target_laps)
        {
            line_params.state = STATE_FINISHED;
            Motor_Control_Left(0);
            Motor_Control_Right(0);
        }
    }
    
    // 离开起始线区域
    if(!line_control.corner_detected && start_line_detected)
    {
        no_start_counter++;
        if(no_start_counter > 100)  // 确保离开起始区域
        {
            start_line_detected = 0;
        }
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     参数显示（通过LED闪烁次数）
// 参数说明     void
// 返回参数     void
// 使用示例     Display_Parameters();
// 备注信息     通过LED闪烁显示当前参数值
//-------------------------------------------------------------------------------------------------------------------
void Display_Parameters(void)
{
    // 这里可以通过串口输出参数，或者通过LED闪烁次数显示
    // 由于没有显示屏，可以通过LED闪烁模式来指示参数
    
    // 简单的参数指示：LED2闪烁次数表示当前参数值
    static uint8 display_value = 0;
    
    switch(line_params.param_mode)
    {
        case PARAM_LAP_COUNT:
            display_value = line_params.target_laps;
            break;
        case PARAM_BASE_SPEED:
            display_value = line_params.base_speed / 100;  // 显示百位数
            break;
        case PARAM_TURN_SPEED:
            display_value = line_params.turn_speed / 50;   // 显示50的倍数
            break;
        case PARAM_SENSITIVITY:
            display_value = line_params.sensitivity;
            break;
        case PARAM_MAX:
        default:
            display_value = 0;
            break;
    }
    
    // 可以在这里添加LED闪烁显示逻辑
}
